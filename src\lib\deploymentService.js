/**
 * Deployment service for static site generation and deployment
 * Handles automatic deployment when posts are published
 */

import { supabase } from './supabase'
import toast from 'react-hot-toast'

// Configuration for different deployment platforms
const DEPLOYMENT_CONFIG = {
  // Cloudflare Pages
  cloudflare: {
    accountId: process.env.VITE_CLOUDFLARE_ACCOUNT_ID,
    projectName: process.env.VITE_CLOUDFLARE_PROJECT_NAME,
    apiToken: process.env.VITE_CLOUDFLARE_API_TOKEN,
    webhookUrl: process.env.VITE_CLOUDFLARE_WEBHOOK_URL
  },
  // Vercel
  vercel: {
    projectId: process.env.VITE_VERCEL_PROJECT_ID,
    teamId: process.env.VITE_VERCEL_TEAM_ID,
    token: process.env.VITE_VERCEL_TOKEN,
    webhookUrl: process.env.VITE_VERCEL_WEBHOOK_URL
  },
  // Netlify
  netlify: {
    siteId: process.env.VITE_NETLIFY_SITE_ID,
    accessToken: process.env.VITE_NETLIFY_ACCESS_TOKEN,
    webhookUrl: process.env.VITE_NETLIFY_WEBHOOK_URL
  },
  // GitHub Pages
  github: {
    owner: process.env.VITE_GITHUB_OWNER,
    repo: process.env.VITE_GITHUB_REPO,
    token: process.env.VITE_GITHUB_TOKEN,
    workflowId: process.env.VITE_GITHUB_WORKFLOW_ID || 'deploy.yml'
  }
}

/**
 * Get the preferred deployment platform
 */
const getDeploymentPlatform = () => {
  // Check which platform is configured
  if (DEPLOYMENT_CONFIG.cloudflare.webhookUrl || DEPLOYMENT_CONFIG.cloudflare.apiToken) {
    return 'cloudflare'
  }
  if (DEPLOYMENT_CONFIG.vercel.webhookUrl || DEPLOYMENT_CONFIG.vercel.token) {
    return 'vercel'
  }
  if (DEPLOYMENT_CONFIG.netlify.webhookUrl || DEPLOYMENT_CONFIG.netlify.accessToken) {
    return 'netlify'
  }
  if (DEPLOYMENT_CONFIG.github.token) {
    return 'github'
  }
  return null
}

/**
 * Trigger deployment via webhook
 */
const triggerWebhookDeployment = async (webhookUrl, postData) => {
  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        trigger: 'post_published',
        post: {
          id: postData.id,
          title: postData.title,
          slug: postData.slug,
          published_at: postData.published_at
        },
        timestamp: new Date().toISOString()
      })
    })

    if (!response.ok) {
      throw new Error(`Webhook failed: ${response.status} ${response.statusText}`)
    }

    return await response.json()
  } catch (error) {
    console.error('Webhook deployment failed:', error)
    throw error
  }
}

/**
 * Deploy to Cloudflare Pages
 */
const deployToCloudflare = async (postData) => {
  const config = DEPLOYMENT_CONFIG.cloudflare

  if (config.webhookUrl) {
    return await triggerWebhookDeployment(config.webhookUrl, postData)
  }

  if (config.apiToken && config.accountId && config.projectName) {
    try {
      const response = await fetch(
        `https://api.cloudflare.com/client/v4/accounts/${config.accountId}/pages/projects/${config.projectName}/deployments`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${config.apiToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            branch: 'main',
            build_config: {
              build_command: 'npm run build',
              destination_dir: 'dist'
            }
          })
        }
      )

      if (!response.ok) {
        throw new Error(`Cloudflare API failed: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Cloudflare deployment failed:', error)
      throw error
    }
  }

  throw new Error('Cloudflare deployment not configured')
}

/**
 * Deploy to Vercel
 */
const deployToVercel = async (postData) => {
  const config = DEPLOYMENT_CONFIG.vercel

  if (config.webhookUrl) {
    return await triggerWebhookDeployment(config.webhookUrl, postData)
  }

  if (config.token && config.projectId) {
    try {
      const response = await fetch(
        `https://api.vercel.com/v13/deployments`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${config.token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: config.projectId,
            gitSource: {
              type: 'github',
              repoId: config.projectId
            },
            target: 'production'
          })
        }
      )

      if (!response.ok) {
        throw new Error(`Vercel API failed: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Vercel deployment failed:', error)
      throw error
    }
  }

  throw new Error('Vercel deployment not configured')
}

/**
 * Deploy to Netlify
 */
const deployToNetlify = async (postData) => {
  const config = DEPLOYMENT_CONFIG.netlify

  if (config.webhookUrl) {
    return await triggerWebhookDeployment(config.webhookUrl, postData)
  }

  if (config.accessToken && config.siteId) {
    try {
      const response = await fetch(
        `https://api.netlify.com/api/v1/sites/${config.siteId}/builds`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${config.accessToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            clear_cache: true
          })
        }
      )

      if (!response.ok) {
        throw new Error(`Netlify API failed: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Netlify deployment failed:', error)
      throw error
    }
  }

  throw new Error('Netlify deployment not configured')
}

/**
 * Deploy via GitHub Actions
 */
const deployToGitHub = async (postData) => {
  const config = DEPLOYMENT_CONFIG.github

  if (!config.token || !config.owner || !config.repo) {
    throw new Error('GitHub deployment not configured')
  }

  try {
    const response = await fetch(
      `https://api.github.com/repos/${config.owner}/${config.repo}/actions/workflows/${config.workflowId}/dispatches`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${config.token}`,
          'Accept': 'application/vnd.github.v3+json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ref: 'main',
          inputs: {
            post_id: postData.id,
            post_slug: postData.slug,
            trigger: 'post_published'
          }
        })
      }
    )

    if (!response.ok) {
      throw new Error(`GitHub API failed: ${response.status}`)
    }

    return { success: true, message: 'GitHub Actions workflow triggered' }
  } catch (error) {
    console.error('GitHub deployment failed:', error)
    throw error
  }
}

/**
 * Log deployment attempt to database
 */
const logDeployment = async (postId, platform, status, details = null) => {
  try {
    await supabase
      .from('deployment_logs')
      .insert([{
        post_id: postId,
        platform,
        status,
        details,
        created_at: new Date().toISOString()
      }])
  } catch (error) {
    console.error('Failed to log deployment:', error)
    // Don't throw - logging failure shouldn't break deployment
  }
}

/**
 * Main deployment function
 * Called when a post is published
 */
export const deployPost = async (postData) => {
  const platform = getDeploymentPlatform()
  
  if (!platform) {
    console.warn('No deployment platform configured')
    toast.error('No deployment platform configured. Please set up deployment environment variables.')
    return { success: false, message: 'No deployment platform configured' }
  }

  const deploymentToast = toast.loading(`Deploying to ${platform}...`)

  try {
    let result

    switch (platform) {
      case 'cloudflare':
        result = await deployToCloudflare(postData)
        break
      case 'vercel':
        result = await deployToVercel(postData)
        break
      case 'netlify':
        result = await deployToNetlify(postData)
        break
      case 'github':
        result = await deployToGitHub(postData)
        break
      default:
        throw new Error(`Unsupported platform: ${platform}`)
    }

    await logDeployment(postData.id, platform, 'success', result)
    
    toast.success(`Successfully deployed to ${platform}!`, { id: deploymentToast })
    
    return { success: true, platform, result }

  } catch (error) {
    console.error('Deployment failed:', error)
    
    await logDeployment(postData.id, platform, 'failed', { error: error.message })
    
    toast.error(`Deployment to ${platform} failed: ${error.message}`, { id: deploymentToast })
    
    return { success: false, platform, error: error.message }
  }
}

/**
 * Check deployment status
 */
export const getDeploymentStatus = async (postId) => {
  try {
    const { data, error } = await supabase
      .from('deployment_logs')
      .select('*')
      .eq('post_id', postId)
      .order('created_at', { ascending: false })
      .limit(5)

    if (error) throw error

    return data || []
  } catch (error) {
    console.error('Failed to get deployment status:', error)
    return []
  }
}

/**
 * Get deployment configuration status
 */
export const getDeploymentConfig = () => {
  const platform = getDeploymentPlatform()
  
  return {
    platform,
    configured: !!platform,
    availablePlatforms: ['cloudflare', 'vercel', 'netlify', 'github']
  }
}
