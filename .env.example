# Supabase Configuration for React App
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Deployment Configuration
# Choose ONE of the following deployment platforms:

# === CLOUDFLARE PAGES ===
# Option 1: Using Webhook (Recommended)
VITE_CLOUDFLARE_WEBHOOK_URL=https://api.cloudflare.com/client/v4/pages/webhooks/deploy_hooks/your_webhook_id

# Option 2: Using API (Advanced)
# VITE_CLOUDFLARE_ACCOUNT_ID=your_account_id
# VITE_CLOUDFLARE_PROJECT_NAME=your_project_name
# VITE_CLOUDFLARE_API_TOKEN=your_api_token

# === VERCEL ===
# Option 1: Using Webhook (Recommended)
VITE_VERCEL_WEBHOOK_URL=https://api.vercel.com/v1/integrations/deploy/your_webhook_id

# Option 2: Using API (Advanced)
# VITE_VERCEL_PROJECT_ID=your_project_id
# VITE_VERCEL_TEAM_ID=your_team_id
# VITE_VERCEL_TOKEN=your_vercel_token

# === NETLIFY ===
# Option 1: Using Webhook (Recommended)
VITE_NETLIFY_WEBHOOK_URL=https://api.netlify.com/build_hooks/your_webhook_id

# Option 2: Using API (Advanced)
# VITE_NETLIFY_SITE_ID=your_site_id
# VITE_NETLIFY_ACCESS_TOKEN=your_access_token

# === GITHUB PAGES ===
# Using GitHub Actions (requires workflow setup)
VITE_GITHUB_OWNER=your_github_username
VITE_GITHUB_REPO=your_repository_name
VITE_GITHUB_TOKEN=your_github_personal_access_token
VITE_GITHUB_WORKFLOW_ID=deploy.yml
