var T=Object.defineProperty,Q=Object.defineProperties;var U=Object.getOwnPropertyDescriptors;var _=Object.getOwnPropertySymbols;var V=Object.prototype.hasOwnProperty,X=Object.prototype.propertyIsEnumerable;var W=(t,s,i)=>s in t?T(t,s,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[s]=i,n=(t,s)=>{for(var i in s||(s={}))V.call(s,i)&&W(t,i,s[i]);if(_)for(var i of _(s))X.call(s,i)&&W(t,i,s[i]);return t},l=(t,s)=>Q(t,U(s));import{r as o,u as D,j as e,L as p}from"./react-vendor-DNThP37t.js";import{d as M,g as F,a as H,f as Y}from"./utils-3gN4sUHA.js";const G=o.memo(({onSearch:t,searchQuery:s,setSearchQuery:i})=>{const x=D(),r=d=>!!(d==="/"&&x.pathname==="/"||d!=="/"&&x.pathname.startsWith(d)),a=o.useCallback(M(d=>{t&&t(d)},300),[t]),m=d=>{const v=d.target.value;i(v),a(v)},u=()=>x.pathname.startsWith("/authors")?"Search Authors...":"Search...";return e.jsxs("div",{className:"header",children:[e.jsx(p,{to:"/",className:"logo",children:"SAYARI"}),e.jsxs("nav",{className:"nav",children:[e.jsx(p,{to:"/",className:`nav-item ${r("/")?"active":""}`,children:"All"}),e.jsx(p,{to:"/category/shayari",className:`nav-item ${r("/category/shayari")?"active":""}`,children:"Shayari"}),e.jsx(p,{to:"/category/quotes",className:`nav-item ${r("/category/quotes")?"active":""}`,children:"Quotes"}),e.jsx(p,{to:"/category/wishes",className:`nav-item ${r("/category/wishes")?"active":""}`,children:"Wishes"}),e.jsx(p,{to:"/authors",className:`nav-item ${r("/authors")?"active":""}`,children:"Authors"})]}),e.jsx("div",{className:"search-container",children:e.jsx("input",{type:"text",className:"search",placeholder:u(),value:s,onChange:m,autoComplete:"off",spellCheck:"false"})})]})});G.displayName="Header";const ee=()=>e.jsx("div",{className:"footer",children:e.jsxs("div",{className:"footer-content",children:[e.jsx("p",{children:"© 2025 Sayari Blog. All rights reserved."}),e.jsx("p",{style:{marginTop:"10px",fontSize:"12px",color:"#999"},children:"A collection of beautiful Hindi Shayari, Quotes, and Wishes"})]})}),f=o.memo(({type:t="post",count:s=1,className:i="",style:x={}})=>{const r=n({backgroundColor:"#f0f0f0",borderRadius:"4px",position:"relative",overflow:"hidden"},x),a={position:"absolute",top:0,left:"-100%",width:"100%",height:"100%",background:"linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent)",animation:"shimmer 1.5s infinite"},m=()=>e.jsxs("div",{className:`skeleton-post ${i}`,style:{marginBottom:"20px"},children:[e.jsx("div",{style:l(n({},r),{width:"100%",height:"200px",marginBottom:"15px"}),children:e.jsx("div",{style:a})}),e.jsx("div",{style:l(n({},r),{width:"80%",height:"24px",marginBottom:"10px"}),children:e.jsx("div",{style:a})}),e.jsx("div",{style:l(n({},r),{width:"100%",height:"16px",marginBottom:"8px"}),children:e.jsx("div",{style:a})}),e.jsx("div",{style:l(n({},r),{width:"90%",height:"16px",marginBottom:"8px"}),children:e.jsx("div",{style:a})}),e.jsx("div",{style:l(n({},r),{width:"70%",height:"16px",marginBottom:"15px"}),children:e.jsx("div",{style:a})}),e.jsxs("div",{style:{display:"flex",gap:"15px"},children:[e.jsx("div",{style:l(n({},r),{width:"80px",height:"14px"}),children:e.jsx("div",{style:a})}),e.jsx("div",{style:l(n({},r),{width:"100px",height:"14px"}),children:e.jsx("div",{style:a})})]})]}),u=()=>e.jsx("div",{className:`skeleton-author ${i}`,style:{marginBottom:"15px"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"15px"},children:[e.jsx("div",{style:l(n({},r),{width:"50px",height:"50px",borderRadius:"50%"}),children:e.jsx("div",{style:a})}),e.jsxs("div",{style:{flex:1},children:[e.jsx("div",{style:l(n({},r),{width:"150px",height:"18px",marginBottom:"5px"}),children:e.jsx("div",{style:a})}),e.jsx("div",{style:l(n({},r),{width:"200px",height:"14px"}),children:e.jsx("div",{style:a})})]})]})}),d=()=>e.jsx("div",{className:`skeleton-header ${i}`,style:{marginBottom:"30px"},children:e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("div",{style:l(n({},r),{width:"120px",height:"32px"}),children:e.jsx("div",{style:a})}),e.jsx("div",{style:{display:"flex",gap:"20px"},children:[1,2,3,4,5].map(h=>e.jsx("div",{style:l(n({},r),{width:"60px",height:"20px"}),children:e.jsx("div",{style:a})},h))}),e.jsx("div",{style:l(n({},r),{width:"200px",height:"36px"}),children:e.jsx("div",{style:a})})]})}),v=({lines:h=3,width:g="100%"})=>e.jsx("div",{className:`skeleton-text ${i}`,children:Array.from({length:h}).map((B,S)=>e.jsx("div",{style:l(n({},r),{width:S===h-1?"70%":g,height:"16px",marginBottom:"8px"}),children:e.jsx("div",{style:a})},S))}),b=({width:h="100%",height:g="200px"})=>e.jsx("div",{className:`skeleton-image ${i}`,style:l(n({},r),{width:h,height:g}),children:e.jsx("div",{style:a})}),I=()=>{switch(t){case"post":return e.jsx(m,{});case"author":return e.jsx(u,{});case"header":return e.jsx(d,{});case"text":return e.jsx(v,{});case"image":return e.jsx(b,{});default:return e.jsx(m,{})}};return e.jsxs(e.Fragment,{children:[e.jsx("style",{children:`
          @keyframes shimmer {
            0% {
              transform: translateX(-100%);
            }
            100% {
              transform: translateX(100%);
            }
          }
        `}),e.jsx("div",{className:`skeleton-loader ${i}`,children:Array.from({length:s}).map((h,g)=>e.jsx("div",{children:I()},g))})]})});f.displayName="SkeletonLoader";o.memo(()=>e.jsx(f,{type:"post",count:1}));o.memo(()=>e.jsx(f,{type:"author",count:1}));o.memo(()=>e.jsx(f,{type:"header",count:1}));o.memo(({lines:t=3})=>e.jsx(f,{type:"text",lines:t}));o.memo(({width:t,height:s})=>e.jsx(f,{type:"image",width:t,height:s}));const C=o.memo(({src:t,alt:s,width:i,height:x,className:r="",style:a={},lazy:m=!0,priority:u=!1,sizes:d="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",aspectRatio:v=null})=>{const[b,I]=o.useState(!1),[h,g]=o.useState(!m||u),[B,S]=o.useState(!1),N=o.useRef(null),R=o.useRef(null);o.useEffect(()=>{if(!m||u||h)return;const c=new IntersectionObserver(w=>{w.forEach(k=>{k.isIntersecting&&(g(!0),c.disconnect())})},{rootMargin:"50px",threshold:.1});return N.current&&(c.observe(N.current),R.current=c),()=>{R.current&&R.current.disconnect()}},[m,u,h]);const L=(c,w,k=80)=>{if(!c)return null;if(c.includes("supabase.co/storage/v1/object/public/"))try{const j=new URL(c);return j.searchParams.set("width",w.toString()),j.searchParams.set("quality",k.toString()),j.searchParams.set("format","webp"),j.toString()}catch(j){return c}return c},z=(c,w="original")=>c?[320,480,640,768,1024,1280,1920].filter($=>!i||$<=i*2).map($=>{let y=L(c,$);if(w==="webp")if(y.includes("supabase.co/storage/v1/object/public/"))try{const A=new URL(y);A.searchParams.set("format","webp"),y=A.toString()}catch(A){}else y=y.replace(/\.(jpg|jpeg|png)$/i,".webp");return`${y} ${$}w`}).join(", "):"",q=()=>{I(!0)},O=()=>{S(!0)},E=e.jsx("div",{className:`image-placeholder ${r}`,style:l(n({},a),{backgroundColor:"#f0f0f0",display:"flex",alignItems:"center",justifyContent:"center",color:"#999",fontSize:"14px"}),ref:N,children:B?"Failed to load":"Loading..."});return!h||B?E:e.jsxs("div",{className:`optimized-image-container ${r}`,style:a,ref:N,children:[e.jsxs("picture",{children:[e.jsx("source",{srcSet:z(t,"webp"),sizes:d,type:"image/webp"}),e.jsx("img",{src:L(t,i),srcSet:z(t),sizes:d,alt:s,width:i,height:x,loading:m&&!u?"lazy":"eager",fetchPriority:u?"high":"auto",decoding:"async",onLoad:q,onError:O,style:{width:"100%",height:"100%",objectFit:"cover",transition:"opacity 0.3s ease",opacity:b?1:0,display:"block"}})]}),!b&&e.jsx("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,backgroundColor:"#f0f0f0",display:"flex",alignItems:"center",justifyContent:"center",color:"#999",fontSize:"14px"},children:e.jsx("div",{style:{width:"16px",height:"16px",border:"2px solid #f3f3f3",borderTop:"2px solid #333",borderRadius:"50%",animation:"spin 1s linear infinite"}})})]})},(t,s)=>t.src===s.src&&t.width===s.width&&t.height===s.height&&t.lazy===s.lazy&&t.priority===s.priority);C.displayName="OptimizedImage";const J=o.memo(({post:t,featured:s=!1,priority:i=!1})=>{const x=()=>"Admin";if(s){const a=F(t.content,t.featured_image_url);return e.jsxs(p,{to:`/${t.slug}`,className:"featured",children:[a&&e.jsx("div",{className:"featured-image",children:e.jsx(C,{src:a,alt:t.title,width:800,height:200,priority:i||s,style:{borderRadius:"8px",marginBottom:"15px"}})}),e.jsx("div",{className:"featured-title",children:t.title}),e.jsx("div",{className:"featured-content",children:H(t.content,200)}),e.jsxs("div",{className:"featured-author",children:["By ",x()]})]})}const r=F(t.content,t.featured_image_url);return e.jsxs(p,{to:`/${t.slug}`,className:"poem-card",children:[r&&e.jsx("div",{className:"poem-image",children:e.jsx(C,{src:r,alt:t.title,width:400,height:150,lazy:!i,priority:i,style:{borderRadius:"8px",marginBottom:"10px"}})}),e.jsx("div",{className:"poem-title",children:t.title}),e.jsx("div",{className:"poem-preview",children:H(t.content)}),e.jsxs("div",{className:"poem-meta",children:[e.jsxs("div",{className:"author",children:["By ",x()]}),e.jsx("div",{className:"date",children:Y(t.published_at)})]})]})},(t,s)=>t.post.id===s.post.id&&t.featured===s.featured&&t.priority===s.priority);J.displayName="PostCard";export{ee as F,G as H,J as P,f as S};
