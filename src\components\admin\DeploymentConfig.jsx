import { useState, useEffect } from 'react'
import { getDeploymentConfig } from '../../lib/deploymentService'

const DeploymentConfig = () => {
  const [config, setConfig] = useState(null)
  const [showInstructions, setShowInstructions] = useState(false)

  useEffect(() => {
    setConfig(getDeploymentConfig())
  }, [])

  const platformInstructions = {
    cloudflare: {
      name: 'Cloudflare Pages',
      steps: [
        'Go to Cloudflare Pages dashboard',
        'Select your project',
        'Go to Settings > Build & Deploy',
        'Create a Deploy Hook',
        'Copy the webhook URL and set VITE_CLOUDFLARE_WEBHOOK_URL in your .env file'
      ],
      envVar: 'VITE_CLOUDFLARE_WEBHOOK_URL'
    },
    vercel: {
      name: 'Vercel',
      steps: [
        'Go to your Vercel project dashboard',
        'Go to Settings > Git',
        'Create a Deploy Hook',
        'Copy the webhook URL and set VITE_VERCEL_WEBHOOK_URL in your .env file'
      ],
      envVar: 'VITE_VERCEL_WEBHOOK_URL'
    },
    netlify: {
      name: 'Netlify',
      steps: [
        'Go to your Netlify site dashboard',
        'Go to Site Settings > Build & Deploy > Build Hooks',
        'Create a new build hook',
        'Copy the webhook URL and set VITE_NETLIFY_WEBHOOK_URL in your .env file'
      ],
      envVar: 'VITE_NETLIFY_WEBHOOK_URL'
    },
    github: {
      name: 'GitHub Pages',
      steps: [
        'Create a GitHub Personal Access Token with repo permissions',
        'Set up a GitHub Actions workflow (see .github/workflows/deploy.yml.example)',
        'Configure VITE_GITHUB_OWNER, VITE_GITHUB_REPO, and VITE_GITHUB_TOKEN in your .env file'
      ],
      envVar: 'VITE_GITHUB_TOKEN'
    }
  }

  if (!config) {
    return <div>Loading deployment configuration...</div>
  }

  return (
    <div className="deployment-config">
      <div className="config-header">
        <h2>Deployment Configuration</h2>
        <button
          onClick={() => setShowInstructions(!showInstructions)}
          className="btn btn-secondary"
        >
          {showInstructions ? 'Hide' : 'Show'} Setup Instructions
        </button>
      </div>

      <div className="config-status">
        {config.configured ? (
          <div className="status-configured">
            <span className="status-icon">✅</span>
            <div className="status-info">
              <strong>Deployment Configured</strong>
              <p>Platform: {config.platform}</p>
              <p>Posts will automatically deploy when published.</p>
            </div>
          </div>
        ) : (
          <div className="status-not-configured">
            <span className="status-icon">⚠️</span>
            <div className="status-info">
              <strong>Deployment Not Configured</strong>
              <p>Set up deployment to automatically publish posts to your static site.</p>
            </div>
          </div>
        )}
      </div>

      {showInstructions && (
        <div className="setup-instructions">
          <h3>Setup Instructions</h3>
          <p>Choose one of the following platforms to enable automatic deployment:</p>
          
          {Object.entries(platformInstructions).map(([key, platform]) => (
            <details key={key} className="platform-instructions">
              <summary>
                <strong>{platform.name}</strong>
                {config.platform === key && <span className="current-platform">(Current)</span>}
              </summary>
              <div className="instruction-content">
                <ol>
                  {platform.steps.map((step, index) => (
                    <li key={index}>{step}</li>
                  ))}
                </ol>
                <div className="env-example">
                  <strong>Environment Variable:</strong>
                  <code>{platform.envVar}=your_webhook_url_or_token</code>
                </div>
              </div>
            </details>
          ))}

          <div className="additional-info">
            <h4>Additional Notes:</h4>
            <ul>
              <li>Copy <code>.env.example</code> to <code>.env</code> and configure your chosen platform</li>
              <li>Restart your development server after adding environment variables</li>
              <li>For production, set these variables in your hosting platform's environment settings</li>
              <li>Webhooks are recommended for simplicity and security</li>
            </ul>
          </div>
        </div>
      )}

      <style jsx>{`
        .deployment-config {
          background: white;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          padding: 2rem;
          margin: 2rem 0;
        }

        .config-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1.5rem;
        }

        .config-header h2 {
          margin: 0;
          color: #1f2937;
        }

        .config-status {
          margin-bottom: 1.5rem;
        }

        .status-configured,
        .status-not-configured {
          display: flex;
          align-items: flex-start;
          gap: 1rem;
          padding: 1rem;
          border-radius: 6px;
        }

        .status-configured {
          background: #f0fdf4;
          border: 1px solid #bbf7d0;
        }

        .status-not-configured {
          background: #fefce8;
          border: 1px solid #fde047;
        }

        .status-icon {
          font-size: 1.5rem;
          line-height: 1;
        }

        .status-info strong {
          display: block;
          margin-bottom: 0.5rem;
          color: #1f2937;
        }

        .status-info p {
          margin: 0.25rem 0;
          color: #6b7280;
        }

        .setup-instructions {
          border-top: 1px solid #e5e7eb;
          padding-top: 1.5rem;
        }

        .setup-instructions h3 {
          margin: 0 0 1rem 0;
          color: #1f2937;
        }

        .platform-instructions {
          margin-bottom: 1rem;
          border: 1px solid #e5e7eb;
          border-radius: 6px;
        }

        .platform-instructions summary {
          padding: 1rem;
          cursor: pointer;
          background: #f9fafb;
          border-radius: 6px 6px 0 0;
          font-weight: 500;
        }

        .platform-instructions[open] summary {
          border-bottom: 1px solid #e5e7eb;
        }

        .current-platform {
          color: #10b981;
          font-size: 0.875rem;
          margin-left: 0.5rem;
        }

        .instruction-content {
          padding: 1rem;
        }

        .instruction-content ol {
          margin: 0 0 1rem 0;
          padding-left: 1.5rem;
        }

        .instruction-content li {
          margin-bottom: 0.5rem;
          color: #374151;
        }

        .env-example {
          background: #f3f4f6;
          padding: 0.75rem;
          border-radius: 4px;
          margin-top: 1rem;
        }

        .env-example code {
          background: #e5e7eb;
          padding: 0.25rem 0.5rem;
          border-radius: 3px;
          font-family: 'Courier New', monospace;
          font-size: 0.875rem;
        }

        .additional-info {
          margin-top: 1.5rem;
          padding: 1rem;
          background: #f8fafc;
          border-radius: 6px;
        }

        .additional-info h4 {
          margin: 0 0 0.75rem 0;
          color: #1f2937;
        }

        .additional-info ul {
          margin: 0;
          padding-left: 1.5rem;
        }

        .additional-info li {
          margin-bottom: 0.5rem;
          color: #374151;
        }

        .additional-info code {
          background: #e5e7eb;
          padding: 0.125rem 0.25rem;
          border-radius: 3px;
          font-family: 'Courier New', monospace;
          font-size: 0.875rem;
        }

        .btn {
          padding: 0.5rem 1rem;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 0.875rem;
          font-weight: 500;
          transition: all 0.2s;
        }

        .btn-secondary {
          background: #6b7280;
          color: white;
        }

        .btn-secondary:hover {
          background: #4b5563;
        }

        @media (max-width: 768px) {
          .config-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
          }

          .status-configured,
          .status-not-configured {
            flex-direction: column;
            text-align: center;
          }
        }
      `}</style>
    </div>
  )
}

export default DeploymentConfig
