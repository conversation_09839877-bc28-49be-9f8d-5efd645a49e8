var h=(t,i,o)=>new Promise((r,l)=>{var m=e=>{try{d(o.next(e))}catch(n){l(n)}},p=e=>{try{d(o.throw(e))}catch(n){l(n)}},d=e=>e.done?r(e.value):Promise.resolve(e.value).then(m,p);d((o=o.apply(t,i)).next())});import{r as a,j as s}from"./react-vendor-DNThP37t.js";import{j as E}from"./utils-3gN4sUHA.js";import{S as L,P as N}from"./components-D616EIDv.js";import"./vendor-BttnBCBn.js";import"./supabase-vendor-DDc5weSN.js";const M=({searchQuery:t})=>{const[i,o]=a.useState([]),[r,l]=a.useState(!0),[m,p]=a.useState(null),[d,e]=a.useState(!0),[n,x]=a.useState(0),v=12;a.useEffect(()=>{f(!0)},[t]);const f=a.useCallback((c=!1)=>h(null,null,function*(){try{l(!0),p(null);const g=c?0:n,{posts:P,hasMore:b}=yield E(g,v,t);c?(o(P),x(1)):(o(u=>[...u,...P]),x(u=>u+1)),e(b)}catch(g){p("Failed to load posts")}finally{l(!1)}}),[n,t]);if(r&&i.length===0)return s.jsx("div",{className:"main-grid",children:s.jsx(L,{type:"post",count:6})});if(m)return s.jsx("div",{className:"main-grid",children:s.jsx("div",{className:"error",children:m})});if(i.length===0&&!r)return s.jsx("div",{className:"main-grid",children:s.jsx("div",{className:"loading",children:t?`No posts found for "${t}"`:"No posts available"})});const[j,...S]=i;return s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"main-grid",children:[j&&!t&&s.jsx(N,{post:j,featured:!0,priority:!0}),(t?i:S).map((c,g)=>s.jsx(N,{post:c,priority:g<3},c.id))]}),d&&!t&&s.jsx("div",{className:"load-more-container",children:s.jsx("button",{onClick:()=>f(!1),disabled:r,className:"load-more-btn",children:r?s.jsxs("div",{className:"loading-inline",children:[s.jsx("div",{className:"spinner",style:{width:"16px",height:"16px",marginRight:"8px"}}),"Loading..."]}):"Load More Posts"})})]})},F=a.memo(M);export{F as default};
