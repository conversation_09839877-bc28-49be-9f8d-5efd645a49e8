<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Sayari Blog - Wireframe</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Helvetica', Arial, sans-serif;
            background: #fafafa;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .wireframe-box {
            background: white;
            border: 1px solid #e0e0e0;
            margin-bottom: 10px;
            position: relative;
        }
        
        .wireframe-box::before {
            content: attr(data-label);
            position: absolute;
            top: -10px;
            left: 15px;
            background: #333;
            color: white;
            padding: 2px 8px;
            font-size: 10px;
            font-weight: bold;
            z-index: 10;
        }
        
        /* Header */
        .header {
            display: grid;
            grid-template-columns: auto 1fr auto;
            align-items: center;
            padding: 20px 30px;
            border-bottom: 1px solid #eee;
        }
        
        .logo {
            width: 120px;
            height: 30px;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .nav {
            display: flex;
            gap: 30px;
            justify-content: center;
        }
        
        .nav-item {
            padding: 8px 0;
            border-bottom: 2px solid transparent;
            font-size: 14px;
            cursor: pointer;
        }
        
        .nav-item.active {
            border-bottom-color: #333;
        }
        
        .search {
            width: 200px;
            height: 35px;
            border: 1px solid #ddd;
            background: #f9f9f9;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        /* Main Grid */
        .main-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .poem-card {
            background: white;
            border: 1px solid #f0f0f0;
            padding: 30px;
            transition: transform 0.2s ease;
            cursor: pointer;
        }
        
        .poem-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .poem-title {
            width: 80%;
            height: 20px;
            background: #333;
            margin-bottom: 15px;
        }
        
        .poem-preview {
            width: 100%;
            height: 80px;
            background: #f5f5f5;
            margin-bottom: 20px;
        }
        
        .poem-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .author {
            width: 80px;
            height: 12px;
            background: #ccc;
        }
        
        .date {
            width: 60px;
            height: 12px;
            background: #e0e0e0;
        }
        
        /* Featured Section */
        .featured {
            grid-column: 1 / -1;
            background: white;
            border: 1px solid #f0f0f0;
            padding: 50px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .featured-title {
            width: 300px;
            height: 35px;
            background: #333;
            margin: 0 auto 20px;
        }
        
        .featured-content {
            width: 400px;
            height: 100px;
            background: #f8f8f8;
            margin: 0 auto 20px;
        }
        
        .featured-author {
            width: 120px;
            height: 15px;
            background: #ccc;
            margin: 0 auto;
        }
        
        /* Category Filter */
        .filter-bar {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border: 1px solid #f0f0f0;
        }
        
        .filter-item {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: #f9f9f9;
            font-size: 12px;
            cursor: pointer;
        }
        
        .filter-item.active {
            background: #333;
            color: white;
            border-color: #333;
        }
        
        /* Footer */
        .footer {
            background: white;
            border: 1px solid #f0f0f0;
            padding: 40px 30px;
            margin-top: 50px;
            text-align: center;
        }
        
        .footer-content {
            width: 200px;
            height: 40px;
            background: #f0f0f0;
            margin: 0 auto;
        }
        
        /* Page Tabs */
        .page-tabs {
            display: flex;
            gap: 0;
            margin-bottom: 20px;
            background: white;
            border: 1px solid #e0e0e0;
        }
        
        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            border-right: 1px solid #e0e0e0;
            cursor: pointer;
            font-weight: 500;
            font-size: 14px;
        }
        
        .tab:last-child {
            border-right: none;
        }
        
        .tab.active {
            background: #333;
            color: white;
        }
        
        .page-content {
            display: none;
        }
        
        .page-content.active {
            display: block;
        }
        
        /* Single Poem Layout */
        .single-poem-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin: 40px 0;
        }
        
        .poem-content {
            background: white;
            border: 1px solid #f0f0f0;
            padding: 50px;
        }
        
        .poem-full-title {
            width: 70%;
            height: 30px;
            background: #333;
            margin-bottom: 30px;
        }
        
        .poem-text {
            width: 100%;
            height: 300px;
            background: #f9f9f9;
            margin-bottom: 30px;
        }
        
        .poem-sidebar {
            display: grid;
            gap: 20px;
        }
        
        .sidebar-section {
            background: white;
            border: 1px solid #f0f0f0;
            padding: 25px;
        }
        
        .sidebar-title {
            width: 100px;
            height: 15px;
            background: #333;
            margin-bottom: 15px;
        }
        
        .sidebar-content {
            width: 100%;
            height: 60px;
            background: #f5f5f5;
        }
        
        /* Author Grid */
        .author-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .author-card {
            background: white;
            border: 1px solid #f0f0f0;
            padding: 30px;
            text-align: center;
        }
        
        .author-avatar {
            width: 60px;
            height: 60px;
            background: #ddd;
            border-radius: 50%;
            margin: 0 auto 15px;
        }
        
        .author-name {
            width: 120px;
            height: 18px;
            background: #333;
            margin: 0 auto 10px;
        }
        
        .author-count {
            width: 80px;
            height: 12px;
            background: #ccc;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Page Navigation -->
        <div class="page-tabs">
            <div class="tab active" onclick="showPage('home')">Home Grid</div>
            <div class="tab" onclick="showPage('single')">Single Poem</div>
            <div class="tab" onclick="showPage('authors')">Authors Grid</div>
        </div>

        <!-- HOME PAGE -->
        <div id="home" class="page-content active">
            <div class="wireframe-box" data-label="HEADER">
                <div class="header">
                    <div class="logo">SAYARI</div>
                    <nav class="nav">
                        <div class="nav-item active">All</div>
                        <div class="nav-item">Love</div>
                        <div class="nav-item">Nature</div>
                        <div class="nav-item">Life</div>
                        <div class="nav-item">Authors</div>
                    </nav>
                    <div class="search">Search...</div>
                </div>
            </div>

            <div class="wireframe-box" data-label="MAIN GRID">
                <div class="main-grid">
                    <!-- Featured Poem - Full Width -->
                    <div class="featured">
                        <div class="featured-title"></div>
                        <div class="featured-content"></div>
                        <div class="featured-author"></div>
                    </div>

                    <!-- Poem Cards -->
                    <div class="poem-card">
                        <div class="poem-title"></div>
                        <div class="poem-preview"></div>
                        <div class="poem-meta">
                            <div class="author"></div>
                            <div class="date"></div>
                        </div>
                    </div>

                    <div class="poem-card">
                        <div class="poem-title"></div>
                        <div class="poem-preview"></div>
                        <div class="poem-meta">
                            <div class="author"></div>
                            <div class="date"></div>
                        </div>
                    </div>

                    <div class="poem-card">
                        <div class="poem-title"></div>
                        <div class="poem-preview"></div>
                        <div class="poem-meta">
                            <div class="author"></div>
                            <div class="date"></div>
                        </div>
                    </div>

                    <div class="poem-card">
                        <div class="poem-title"></div>
                        <div class="poem-preview"></div>
                        <div class="poem-meta">
                            <div class="author"></div>
                            <div class="date"></div>
                        </div>
                    </div>

                    <div class="poem-card">
                        <div class="poem-title"></div>
                        <div class="poem-preview"></div>
                        <div class="poem-meta">
                            <div class="author"></div>
                            <div class="date"></div>
                        </div>
                    </div>

                    <div class="poem-card">
                        <div class="poem-title"></div>
                        <div class="poem-preview"></div>
                        <div class="poem-meta">
                            <div class="author"></div>
                            <div class="date"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="wireframe-box" data-label="FOOTER">
                <div class="footer">
                    <div class="footer-content"></div>
                </div>
            </div>
        </div>

        <!-- SINGLE POEM PAGE -->
        <div id="single" class="page-content">
            <div class="wireframe-box" data-label="HEADER">
                <div class="header">
                    <div class="logo">SAYARI</div>
                    <nav class="nav">
                        <div class="nav-item">← Back</div>
                        <div class="nav-item">Share</div>
                        <div class="nav-item">Save</div>
                    </nav>
                    <div class="search">Search...</div>
                </div>
            </div>

            <div class="wireframe-box" data-label="SINGLE POEM LAYOUT">
                <div class="single-poem-grid">
                    <div class="poem-content">
                        <div class="poem-full-title"></div>
                        <div class="poem-meta" style="margin-bottom: 30px;">
                            <div class="author" style="width: 100px;"></div>
                        </div>
                        <div class="poem-text"></div>
                    </div>

                    <div class="poem-sidebar">
                        <div class="sidebar-section">
                            <div class="sidebar-title"></div>
                            <div class="sidebar-content"></div>
                        </div>
                        <div class="sidebar-section">
                            <div class="sidebar-title"></div>
                            <div class="sidebar-content"></div>
                        </div>
                        <div class="sidebar-section">
                            <div class="sidebar-title"></div>
                            <div class="sidebar-content"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AUTHORS PAGE -->
        <div id="authors" class="page-content">
            <div class="wireframe-box" data-label="HEADER">
                <div class="header">
                    <div class="logo">SAYARI</div>
                    <nav class="nav">
                        <div class="nav-item">Home</div>
                        <div class="nav-item active">Authors</div>
                        <div class="nav-item">Categories</div>
                    </nav>
                    <div class="search">Search Authors...</div>
                </div>
            </div>

            <div class="wireframe-box" data-label="AUTHORS GRID">
                <div class="author-grid">
                    <div class="author-card">
                        <div class="author-avatar"></div>
                        <div class="author-name"></div>
                        <div class="author-count"></div>
                    </div>
                    <div class="author-card">
                        <div class="author-avatar"></div>
                        <div class="author-name"></div>
                        <div class="author-count"></div>
                    </div>
                    <div class="author-card">
                        <div class="author-avatar"></div>
                        <div class="author-name"></div>
                        <div class="author-count"></div>
                    </div>
                    <div class="author-card">
                        <div class="author-avatar"></div>
                        <div class="author-name"></div>
                        <div class="author-count"></div>
                    </div>
                    <div class="author-card">
                        <div class="author-avatar"></div>
                        <div class="author-name"></div>
                        <div class="author-count"></div>
                    </div>
                    <div class="author-card">
                        <div class="author-avatar"></div>
                        <div class="author-name"></div>
                        <div class="author-count"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showPage(pageId) {
            // Hide all pages
            const pages = document.querySelectorAll('.page-content');
            pages.forEach(page => page.classList.remove('active'));
            
            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // Show selected page
            document.getElementById(pageId).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }
    </script>
</body>
</html>