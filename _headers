# Static Site Generation Headers for Cloudflare Pages

# Cache static assets for 1 year
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# Cache static data files for 1 hour (can be updated more frequently)
/data/static/*
  Cache-Control: public, max-age=3600

# Cache HTML files for 1 hour with revalidation
/*.html
  Cache-Control: public, max-age=3600, must-revalidate

# Cache root index for 1 hour
/
  Cache-Control: public, max-age=3600, must-revalidate

# Security headers for all pages
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
