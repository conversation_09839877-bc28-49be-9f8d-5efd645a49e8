const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/page-home-hm4vwBai.js","assets/js/react-vendor-DNThP37t.js","assets/js/vendor-BttnBCBn.js","assets/js/utils-3gN4sUHA.js","assets/js/supabase-vendor-DDc5weSN.js","assets/js/components-D616EIDv.js","assets/js/page-post-4vpf1Y-I.js","assets/js/page-authors-DFhGiWFB.js","assets/js/page-author-LCGEjuGj.js","assets/js/page-category-9WFtrx1I.js","assets/js/page-tag-C0IOWwHL.js"])))=>i.map(i=>d[i]);
import{r,j as e,B as p,R as m,c as a,d as _}from"./react-vendor-DNThP37t.js";import{_ as i}from"./vendor-BttnBCBn.js";import{H as f,S as h,F as x}from"./components-D616EIDv.js";import{o as y,r as j}from"./utils-3gN4sUHA.js";import"./supabase-vendor-DDc5weSN.js";(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const t of document.querySelectorAll('link[rel="modulepreload"]'))c(t);new MutationObserver(t=>{for(const o of t)if(o.type==="childList")for(const u of o.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&c(u)}).observe(document,{childList:!0,subtree:!0});function l(t){const o={};return t.integrity&&(o.integrity=t.integrity),t.referrerPolicy&&(o.referrerPolicy=t.referrerPolicy),t.crossOrigin==="use-credentials"?o.credentials="include":t.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function c(t){if(t.ep)return;t.ep=!0;const o=l(t);fetch(t.href,o)}})();const g=r.lazy(()=>i(()=>import("./page-home-hm4vwBai.js"),__vite__mapDeps([0,1,2,3,4,5]))),E=r.lazy(()=>i(()=>import("./page-post-4vpf1Y-I.js"),__vite__mapDeps([6,1,2,3,4,5]))),S=r.lazy(()=>i(()=>import("./page-authors-DFhGiWFB.js"),__vite__mapDeps([7,1,2,3,4]))),P=r.lazy(()=>i(()=>import("./page-author-LCGEjuGj.js"),__vite__mapDeps([8,1,2,3,4,5]))),L=r.lazy(()=>i(()=>import("./page-category-9WFtrx1I.js"),__vite__mapDeps([9,1,2,3,4,5]))),O=r.lazy(()=>i(()=>import("./page-tag-C0IOWwHL.js"),__vite__mapDeps([10,1,2,3,4,5]))),d=r.memo(()=>{const[s,n]=r.useState(""),l=r.useCallback(t=>{n(t)},[]),c=r.useCallback(t=>{n(t)},[]);return e.jsx(p,{children:e.jsxs("div",{className:"container",children:[e.jsx(f,{onSearch:l,searchQuery:s,setSearchQuery:c}),e.jsx(r.Suspense,{fallback:e.jsx(h,{type:"post",count:3}),children:e.jsxs(m,{children:[e.jsx(a,{path:"/",element:e.jsx(g,{searchQuery:s})}),e.jsx(a,{path:"/authors",element:e.jsx(S,{searchQuery:s})}),e.jsx(a,{path:"/author/:username",element:e.jsx(P,{searchQuery:s})}),e.jsx(a,{path:"/category/:slug",element:e.jsx(L,{searchQuery:s})}),e.jsx(a,{path:"/tag/:slug",element:e.jsx(O,{searchQuery:s})}),e.jsx(a,{path:"/:slug",element:e.jsx(E,{})})]})}),e.jsx(x,{})]})})});d.displayName="App";_.createRoot(document.getElementById("root")).render(e.jsx(r.StrictMode,{children:e.jsx(d,{})}));y();j({onSuccess:()=>{},onUpdate:()=>{}});
