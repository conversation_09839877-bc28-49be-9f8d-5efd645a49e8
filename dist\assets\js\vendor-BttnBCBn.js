var h=(n,e,t)=>new Promise((s,r)=>{var i=l=>{try{a(t.next(l))}catch(c){r(c)}},o=l=>{try{a(t.throw(l))}catch(c){r(c)}},a=l=>l.done?s(l.value):Promise.resolve(l.value).then(i,o);a((t=t.apply(n,e)).next())});import{g as rs,b as is}from"./react-vendor-DNThP37t.js";var Ue={exports:{}},Le={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ct;function ns(){return ct||(ct=1,function(n){function e(v,b){var k=v.length;v.push(b);e:for(;0<k;){var $=k-1>>>1,I=v[$];if(0<r(I,b))v[$]=b,v[k]=I,k=$;else break e}}function t(v){return v.length===0?null:v[0]}function s(v){if(v.length===0)return null;var b=v[0],k=v.pop();if(k!==b){v[0]=k;e:for(var $=0,I=v.length,ke=I>>>1;$<ke;){var z=2*($+1)-1,xe=v[z],H=z+1,Se=v[H];if(0>r(xe,k))H<I&&0>r(Se,xe)?(v[$]=Se,v[H]=k,$=H):(v[$]=xe,v[z]=k,$=z);else if(H<I&&0>r(Se,k))v[$]=Se,v[H]=k,$=H;else break e}}return b}function r(v,b){var k=v.sortIndex-b.sortIndex;return k!==0?k:v.id-b.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;n.unstable_now=function(){return i.now()}}else{var o=Date,a=o.now();n.unstable_now=function(){return o.now()-a}}var l=[],c=[],u=1,d=null,f=3,g=!1,p=!1,w=!1,_=typeof setTimeout=="function"?setTimeout:null,j=typeof clearTimeout=="function"?clearTimeout:null,A=typeof setImmediate!="undefined"?setImmediate:null;typeof navigator!="undefined"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(v){for(var b=t(c);b!==null;){if(b.callback===null)s(c);else if(b.startTime<=v)s(c),b.sortIndex=b.expirationTime,e(l,b);else break;b=t(c)}}function E(v){if(w=!1,y(v),!p)if(t(l)!==null)p=!0,Ie(O);else{var b=t(c);b!==null&&Ce(E,b.startTime-v)}}function O(v,b){p=!1,w&&(w=!1,j(fe),fe=-1),g=!0;var k=f;try{for(y(b),d=t(l);d!==null&&(!(d.expirationTime>b)||v&&!at());){var $=d.callback;if(typeof $=="function"){d.callback=null,f=d.priorityLevel;var I=$(d.expirationTime<=b);b=n.unstable_now(),typeof I=="function"?d.callback=I:d===t(l)&&s(l),y(b)}else s(l);d=t(l)}if(d!==null)var ke=!0;else{var z=t(c);z!==null&&Ce(E,z.startTime-b),ke=!1}return ke}finally{d=null,f=k,g=!1}}var P=!1,R=null,fe=-1,nt=5,ot=-1;function at(){return!(n.unstable_now()-ot<nt)}function Re(){if(R!==null){var v=n.unstable_now();ot=v;var b=!0;try{b=R(!0,v)}finally{b?ge():(P=!1,R=null)}}else P=!1}var ge;if(typeof A=="function")ge=function(){A(Re)};else if(typeof MessageChannel!="undefined"){var lt=new MessageChannel,ss=lt.port2;lt.port1.onmessage=Re,ge=function(){ss.postMessage(null)}}else ge=function(){_(Re,0)};function Ie(v){R=v,P||(P=!0,ge())}function Ce(v,b){fe=_(function(){v(n.unstable_now())},b)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(v){v.callback=null},n.unstable_continueExecution=function(){p||g||(p=!0,Ie(O))},n.unstable_forceFrameRate=function(v){0>v||125<v||(nt=0<v?Math.floor(1e3/v):5)},n.unstable_getCurrentPriorityLevel=function(){return f},n.unstable_getFirstCallbackNode=function(){return t(l)},n.unstable_next=function(v){switch(f){case 1:case 2:case 3:var b=3;break;default:b=f}var k=f;f=b;try{return v()}finally{f=k}},n.unstable_pauseExecution=function(){},n.unstable_requestPaint=function(){},n.unstable_runWithPriority=function(v,b){switch(v){case 1:case 2:case 3:case 4:case 5:break;default:v=3}var k=f;f=v;try{return b()}finally{f=k}},n.unstable_scheduleCallback=function(v,b,k){var $=n.unstable_now();switch(typeof k=="object"&&k!==null?(k=k.delay,k=typeof k=="number"&&0<k?$+k:$):k=$,v){case 1:var I=-1;break;case 2:I=250;break;case 5:I=1073741823;break;case 4:I=1e4;break;default:I=5e3}return I=k+I,v={id:u++,callback:b,priorityLevel:v,startTime:k,expirationTime:I,sortIndex:-1},k>$?(v.sortIndex=k,e(c,v),t(l)===null&&v===t(c)&&(w?(j(fe),fe=-1):w=!0,Ce(E,k-$))):(v.sortIndex=I,e(l,v),p||g||(p=!0,Ie(O))),v},n.unstable_shouldYield=at,n.unstable_wrapCallback=function(v){var b=f;return function(){var k=f;f=b;try{return v.apply(this,arguments)}finally{f=k}}}}(Le)),Le}var ut;function Wr(){return ut||(ut=1,Ue.exports=ns()),Ue.exports}const os="modulepreload",as=function(n){return"/"+n},ht={},me=function(e,t,s){let r=Promise.resolve();if(t&&t.length>0){let l=function(c){return Promise.all(c.map(u=>Promise.resolve(u).then(d=>({status:"fulfilled",value:d}),d=>({status:"rejected",reason:d}))))};document.getElementsByTagName("link");const o=document.querySelector("meta[property=csp-nonce]"),a=(o==null?void 0:o.nonce)||(o==null?void 0:o.getAttribute("nonce"));r=l(t.map(c=>{if(c=as(c),c in ht)return;ht[c]=!0;const u=c.endsWith(".css"),d=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${d}`))return;const f=document.createElement("link");if(f.rel=u?"stylesheet":os,u||(f.as="script"),f.crossOrigin="",f.href=c,a&&f.setAttribute("nonce",a),document.head.appendChild(f),u)return new Promise((g,p)=>{f.addEventListener("load",g),f.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${c}`)))})}))}function i(o){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=o,window.dispatchEvent(a),!a.defaultPrevented)throw o}return r.then(o=>{for(const a of o||[])a.status==="rejected"&&i(a.reason);return e().catch(i)})},ls=n=>{let e;return n?e=n:typeof fetch=="undefined"?e=(...t)=>me(()=>h(null,null,function*(){const{default:s}=yield Promise.resolve().then(()=>de);return{default:s}}),void 0).then(({default:s})=>s(...t)):e=fetch,(...t)=>e(...t)};class et extends Error{constructor(e,t="FunctionsError",s){super(e),this.name=t,this.context=s}}class cs extends et{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class dt extends et{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class ft extends et{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var Ke;(function(n){n.Any="any",n.ApNortheast1="ap-northeast-1",n.ApNortheast2="ap-northeast-2",n.ApSouth1="ap-south-1",n.ApSoutheast1="ap-southeast-1",n.ApSoutheast2="ap-southeast-2",n.CaCentral1="ca-central-1",n.EuCentral1="eu-central-1",n.EuWest1="eu-west-1",n.EuWest2="eu-west-2",n.EuWest3="eu-west-3",n.SaEast1="sa-east-1",n.UsEast1="us-east-1",n.UsWest1="us-west-1",n.UsWest2="us-west-2"})(Ke||(Ke={}));var us=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(o){o(i)})}return new(t||(t=Promise))(function(i,o){function a(u){try{c(s.next(u))}catch(d){o(d)}}function l(u){try{c(s.throw(u))}catch(d){o(d)}}function c(u){u.done?i(u.value):r(u.value).then(a,l)}c((s=s.apply(n,e||[])).next())})};class Kr{constructor(e,{headers:t={},customFetch:s,region:r=Ke.Any}={}){this.url=e,this.headers=t,this.region=r,this.fetch=ls(s)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var s;return us(this,void 0,void 0,function*(){try{const{headers:r,method:i,body:o}=t;let a={},{region:l}=t;l||(l=this.region);const c=new URL(`${this.url}/${e}`);l&&l!=="any"&&(a["x-region"]=l,c.searchParams.set("forceFunctionRegion",l));let u;o&&(r&&!Object.prototype.hasOwnProperty.call(r,"Content-Type")||!r)&&(typeof Blob!="undefined"&&o instanceof Blob||o instanceof ArrayBuffer?(a["Content-Type"]="application/octet-stream",u=o):typeof o=="string"?(a["Content-Type"]="text/plain",u=o):typeof FormData!="undefined"&&o instanceof FormData?u=o:(a["Content-Type"]="application/json",u=JSON.stringify(o)));const d=yield this.fetch(c.toString(),{method:i||"POST",headers:Object.assign(Object.assign(Object.assign({},a),this.headers),r),body:u}).catch(w=>{throw new cs(w)}),f=d.headers.get("x-relay-error");if(f&&f==="true")throw new dt(d);if(!d.ok)throw new ft(d);let g=((s=d.headers.get("Content-Type"))!==null&&s!==void 0?s:"text/plain").split(";")[0].trim(),p;return g==="application/json"?p=yield d.json():g==="application/octet-stream"?p=yield d.blob():g==="text/event-stream"?p=d:g==="multipart/form-data"?p=yield d.formData():p=yield d.text(),{data:p,error:null,response:d}}catch(r){return{data:null,error:r,response:r instanceof ft||r instanceof dt?r.context:void 0}}})}}var C={},Z={},ee={},te={},se={},re={},hs=function(){if(typeof self!="undefined")return self;if(typeof window!="undefined")return window;if(typeof global!="undefined")return global;throw new Error("unable to locate global object")},he=hs();const ds=he.fetch,fs=he.fetch.bind(he),gs=he.Headers,ps=he.Request,vs=he.Response,de=Object.freeze(Object.defineProperty({__proto__:null,Headers:gs,Request:ps,Response:vs,default:fs,fetch:ds},Symbol.toStringTag,{value:"Module"})),_s=rs(de);var Ee={},gt;function Bt(){if(gt)return Ee;gt=1,Object.defineProperty(Ee,"__esModule",{value:!0});class n extends Error{constructor(t){super(t.message),this.name="PostgrestError",this.details=t.details,this.hint=t.hint,this.code=t.code}}return Ee.default=n,Ee}var pt;function Nt(){if(pt)return re;pt=1;var n=re&&re.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(re,"__esModule",{value:!0});const e=n(_s),t=n(Bt());class s{constructor(i){this.shouldThrowOnError=!1,this.method=i.method,this.url=i.url,this.headers=i.headers,this.schema=i.schema,this.body=i.body,this.shouldThrowOnError=i.shouldThrowOnError,this.signal=i.signal,this.isMaybeSingle=i.isMaybeSingle,i.fetch?this.fetch=i.fetch:typeof fetch=="undefined"?this.fetch=e.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(i,o){return this.headers=Object.assign({},this.headers),this.headers[i]=o,this}then(i,o){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const a=this.fetch;let l=a(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(c=>h(this,null,function*(){var u,d,f;let g=null,p=null,w=null,_=c.status,j=c.statusText;if(c.ok){if(this.method!=="HEAD"){const O=yield c.text();O===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?p=O:p=JSON.parse(O))}const y=(u=this.headers.Prefer)===null||u===void 0?void 0:u.match(/count=(exact|planned|estimated)/),E=(d=c.headers.get("content-range"))===null||d===void 0?void 0:d.split("/");y&&E&&E.length>1&&(w=parseInt(E[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(p)&&(p.length>1?(g={code:"PGRST116",details:`Results contain ${p.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},p=null,w=null,_=406,j="Not Acceptable"):p.length===1?p=p[0]:p=null)}else{const y=yield c.text();try{g=JSON.parse(y),Array.isArray(g)&&c.status===404&&(p=[],g=null,_=200,j="OK")}catch(E){c.status===404&&y===""?(_=204,j="No Content"):g={message:y}}if(g&&this.isMaybeSingle&&(!((f=g==null?void 0:g.details)===null||f===void 0)&&f.includes("0 rows"))&&(g=null,_=200,j="OK"),g&&this.shouldThrowOnError)throw new t.default(g)}return{error:g,data:p,count:w,status:_,statusText:j}}));return this.shouldThrowOnError||(l=l.catch(c=>{var u,d,f;return{error:{message:`${(u=c==null?void 0:c.name)!==null&&u!==void 0?u:"FetchError"}: ${c==null?void 0:c.message}`,details:`${(d=c==null?void 0:c.stack)!==null&&d!==void 0?d:""}`,hint:"",code:`${(f=c==null?void 0:c.code)!==null&&f!==void 0?f:""}`},data:null,count:null,status:0,statusText:""}})),l.then(i,o)}returns(){return this}overrideTypes(){return this}}return re.default=s,re}var vt;function Mt(){if(vt)return se;vt=1;var n=se&&se.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(se,"__esModule",{value:!0});const e=n(Nt());class t extends e.default{select(r){let i=!1;const o=(r!=null?r:"*").split("").map(a=>/\s/.test(a)&&!i?"":(a==='"'&&(i=!i),a)).join("");return this.url.searchParams.set("select",o),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(r,{ascending:i=!0,nullsFirst:o,foreignTable:a,referencedTable:l=a}={}){const c=l?`${l}.order`:"order",u=this.url.searchParams.get(c);return this.url.searchParams.set(c,`${u?`${u},`:""}${r}.${i?"asc":"desc"}${o===void 0?"":o?".nullsfirst":".nullslast"}`),this}limit(r,{foreignTable:i,referencedTable:o=i}={}){const a=typeof o=="undefined"?"limit":`${o}.limit`;return this.url.searchParams.set(a,`${r}`),this}range(r,i,{foreignTable:o,referencedTable:a=o}={}){const l=typeof a=="undefined"?"offset":`${a}.offset`,c=typeof a=="undefined"?"limit":`${a}.limit`;return this.url.searchParams.set(l,`${r}`),this.url.searchParams.set(c,`${i-r+1}`),this}abortSignal(r){return this.signal=r,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:r=!1,verbose:i=!1,settings:o=!1,buffers:a=!1,wal:l=!1,format:c="text"}={}){var u;const d=[r?"analyze":null,i?"verbose":null,o?"settings":null,a?"buffers":null,l?"wal":null].filter(Boolean).join("|"),f=(u=this.headers.Accept)!==null&&u!==void 0?u:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${c}; for="${f}"; options=${d};`,c==="json"?this:this}rollback(){var r;return((r=this.headers.Prefer)!==null&&r!==void 0?r:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}return se.default=t,se}var _t;function tt(){if(_t)return te;_t=1;var n=te&&te.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(te,"__esModule",{value:!0});const e=n(Mt());class t extends e.default{eq(r,i){return this.url.searchParams.append(r,`eq.${i}`),this}neq(r,i){return this.url.searchParams.append(r,`neq.${i}`),this}gt(r,i){return this.url.searchParams.append(r,`gt.${i}`),this}gte(r,i){return this.url.searchParams.append(r,`gte.${i}`),this}lt(r,i){return this.url.searchParams.append(r,`lt.${i}`),this}lte(r,i){return this.url.searchParams.append(r,`lte.${i}`),this}like(r,i){return this.url.searchParams.append(r,`like.${i}`),this}likeAllOf(r,i){return this.url.searchParams.append(r,`like(all).{${i.join(",")}}`),this}likeAnyOf(r,i){return this.url.searchParams.append(r,`like(any).{${i.join(",")}}`),this}ilike(r,i){return this.url.searchParams.append(r,`ilike.${i}`),this}ilikeAllOf(r,i){return this.url.searchParams.append(r,`ilike(all).{${i.join(",")}}`),this}ilikeAnyOf(r,i){return this.url.searchParams.append(r,`ilike(any).{${i.join(",")}}`),this}is(r,i){return this.url.searchParams.append(r,`is.${i}`),this}in(r,i){const o=Array.from(new Set(i)).map(a=>typeof a=="string"&&new RegExp("[,()]").test(a)?`"${a}"`:`${a}`).join(",");return this.url.searchParams.append(r,`in.(${o})`),this}contains(r,i){return typeof i=="string"?this.url.searchParams.append(r,`cs.${i}`):Array.isArray(i)?this.url.searchParams.append(r,`cs.{${i.join(",")}}`):this.url.searchParams.append(r,`cs.${JSON.stringify(i)}`),this}containedBy(r,i){return typeof i=="string"?this.url.searchParams.append(r,`cd.${i}`):Array.isArray(i)?this.url.searchParams.append(r,`cd.{${i.join(",")}}`):this.url.searchParams.append(r,`cd.${JSON.stringify(i)}`),this}rangeGt(r,i){return this.url.searchParams.append(r,`sr.${i}`),this}rangeGte(r,i){return this.url.searchParams.append(r,`nxl.${i}`),this}rangeLt(r,i){return this.url.searchParams.append(r,`sl.${i}`),this}rangeLte(r,i){return this.url.searchParams.append(r,`nxr.${i}`),this}rangeAdjacent(r,i){return this.url.searchParams.append(r,`adj.${i}`),this}overlaps(r,i){return typeof i=="string"?this.url.searchParams.append(r,`ov.${i}`):this.url.searchParams.append(r,`ov.{${i.join(",")}}`),this}textSearch(r,i,{config:o,type:a}={}){let l="";a==="plain"?l="pl":a==="phrase"?l="ph":a==="websearch"&&(l="w");const c=o===void 0?"":`(${o})`;return this.url.searchParams.append(r,`${l}fts${c}.${i}`),this}match(r){return Object.entries(r).forEach(([i,o])=>{this.url.searchParams.append(i,`eq.${o}`)}),this}not(r,i,o){return this.url.searchParams.append(r,`not.${i}.${o}`),this}or(r,{foreignTable:i,referencedTable:o=i}={}){const a=o?`${o}.or`:"or";return this.url.searchParams.append(a,`(${r})`),this}filter(r,i,o){return this.url.searchParams.append(r,`${i}.${o}`),this}}return te.default=t,te}var yt;function Ft(){if(yt)return ee;yt=1;var n=ee&&ee.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(ee,"__esModule",{value:!0});const e=n(tt());class t{constructor(r,{headers:i={},schema:o,fetch:a}){this.url=r,this.headers=i,this.schema=o,this.fetch=a}select(r,{head:i=!1,count:o}={}){const a=i?"HEAD":"GET";let l=!1;const c=(r!=null?r:"*").split("").map(u=>/\s/.test(u)&&!l?"":(u==='"'&&(l=!l),u)).join("");return this.url.searchParams.set("select",c),o&&(this.headers.Prefer=`count=${o}`),new e.default({method:a,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(r,{count:i,defaultToNull:o=!0}={}){const a="POST",l=[];if(this.headers.Prefer&&l.push(this.headers.Prefer),i&&l.push(`count=${i}`),o||l.push("missing=default"),this.headers.Prefer=l.join(","),Array.isArray(r)){const c=r.reduce((u,d)=>u.concat(Object.keys(d)),[]);if(c.length>0){const u=[...new Set(c)].map(d=>`"${d}"`);this.url.searchParams.set("columns",u.join(","))}}return new e.default({method:a,url:this.url,headers:this.headers,schema:this.schema,body:r,fetch:this.fetch,allowEmpty:!1})}upsert(r,{onConflict:i,ignoreDuplicates:o=!1,count:a,defaultToNull:l=!0}={}){const c="POST",u=[`resolution=${o?"ignore":"merge"}-duplicates`];if(i!==void 0&&this.url.searchParams.set("on_conflict",i),this.headers.Prefer&&u.push(this.headers.Prefer),a&&u.push(`count=${a}`),l||u.push("missing=default"),this.headers.Prefer=u.join(","),Array.isArray(r)){const d=r.reduce((f,g)=>f.concat(Object.keys(g)),[]);if(d.length>0){const f=[...new Set(d)].map(g=>`"${g}"`);this.url.searchParams.set("columns",f.join(","))}}return new e.default({method:c,url:this.url,headers:this.headers,schema:this.schema,body:r,fetch:this.fetch,allowEmpty:!1})}update(r,{count:i}={}){const o="PATCH",a=[];return this.headers.Prefer&&a.push(this.headers.Prefer),i&&a.push(`count=${i}`),this.headers.Prefer=a.join(","),new e.default({method:o,url:this.url,headers:this.headers,schema:this.schema,body:r,fetch:this.fetch,allowEmpty:!1})}delete({count:r}={}){const i="DELETE",o=[];return r&&o.push(`count=${r}`),this.headers.Prefer&&o.unshift(this.headers.Prefer),this.headers.Prefer=o.join(","),new e.default({method:i,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}return ee.default=t,ee}var pe={},ve={},wt;function ys(){return wt||(wt=1,Object.defineProperty(ve,"__esModule",{value:!0}),ve.version=void 0,ve.version="0.0.0-automated"),ve}var mt;function ws(){if(mt)return pe;mt=1,Object.defineProperty(pe,"__esModule",{value:!0}),pe.DEFAULT_HEADERS=void 0;const n=ys();return pe.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${n.version}`},pe}var bt;function ms(){if(bt)return Z;bt=1;var n=Z&&Z.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(Z,"__esModule",{value:!0});const e=n(Ft()),t=n(tt()),s=ws();class r{constructor(o,{headers:a={},schema:l,fetch:c}={}){this.url=o,this.headers=Object.assign(Object.assign({},s.DEFAULT_HEADERS),a),this.schemaName=l,this.fetch=c}from(o){const a=new URL(`${this.url}/${o}`);return new e.default(a,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(o){return new r(this.url,{headers:this.headers,schema:o,fetch:this.fetch})}rpc(o,a={},{head:l=!1,get:c=!1,count:u}={}){let d;const f=new URL(`${this.url}/rpc/${o}`);let g;l||c?(d=l?"HEAD":"GET",Object.entries(a).filter(([w,_])=>_!==void 0).map(([w,_])=>[w,Array.isArray(_)?`{${_.join(",")}}`:`${_}`]).forEach(([w,_])=>{f.searchParams.append(w,_)})):(d="POST",g=a);const p=Object.assign({},this.headers);return u&&(p.Prefer=`count=${u}`),new t.default({method:d,url:f,headers:p,schema:this.schemaName,body:g,fetch:this.fetch,allowEmpty:!1})}}return Z.default=r,Z}var kt;function bs(){if(kt)return C;kt=1;var n=C&&C.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(C,"__esModule",{value:!0}),C.PostgrestError=C.PostgrestBuilder=C.PostgrestTransformBuilder=C.PostgrestFilterBuilder=C.PostgrestQueryBuilder=C.PostgrestClient=void 0;const e=n(ms());C.PostgrestClient=e.default;const t=n(Ft());C.PostgrestQueryBuilder=t.default;const s=n(tt());C.PostgrestFilterBuilder=s.default;const r=n(Mt());C.PostgrestTransformBuilder=r.default;const i=n(Nt());C.PostgrestBuilder=i.default;const o=n(Bt());return C.PostgrestError=o.default,C.default={PostgrestClient:e.default,PostgrestQueryBuilder:t.default,PostgrestFilterBuilder:s.default,PostgrestTransformBuilder:r.default,PostgrestBuilder:i.default,PostgrestError:o.default},C}var ks=bs();const Ss=is(ks),{PostgrestClient:Jr,PostgrestQueryBuilder:zr,PostgrestFilterBuilder:Hr,PostgrestTransformBuilder:Gr,PostgrestBuilder:Vr,PostgrestError:Qr}=Ss;function Es(){if(typeof WebSocket!="undefined")return WebSocket;if(typeof global.WebSocket!="undefined")return global.WebSocket;if(typeof window.WebSocket!="undefined")return window.WebSocket;if(typeof self.WebSocket!="undefined")return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}const Ps=Es(),js="2.11.15",Ts=`realtime-js/${js}`,$s="1.0.0",Wt=1e4,Os=1e3;var _e;(function(n){n[n.connecting=0]="connecting",n[n.open=1]="open",n[n.closing=2]="closing",n[n.closed=3]="closed"})(_e||(_e={}));var U;(function(n){n.closed="closed",n.errored="errored",n.joined="joined",n.joining="joining",n.leaving="leaving"})(U||(U={}));var q;(function(n){n.close="phx_close",n.error="phx_error",n.join="phx_join",n.reply="phx_reply",n.leave="phx_leave",n.access_token="access_token"})(q||(q={}));var Je;(function(n){n.websocket="websocket"})(Je||(Je={}));var Y;(function(n){n.Connecting="connecting",n.Open="open",n.Closing="closing",n.Closed="closed"})(Y||(Y={}));class As{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t(typeof e=="string"?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),s=new TextDecoder;return this._decodeBroadcast(e,t,s)}_decodeBroadcast(e,t,s){const r=t.getUint8(1),i=t.getUint8(2);let o=this.HEADER_LENGTH+2;const a=s.decode(e.slice(o,o+r));o=o+r;const l=s.decode(e.slice(o,o+i));o=o+i;const c=JSON.parse(s.decode(e.slice(o,e.byteLength)));return{ref:null,topic:a,event:l,payload:c}}}class Kt{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var T;(function(n){n.abstime="abstime",n.bool="bool",n.date="date",n.daterange="daterange",n.float4="float4",n.float8="float8",n.int2="int2",n.int4="int4",n.int4range="int4range",n.int8="int8",n.int8range="int8range",n.json="json",n.jsonb="jsonb",n.money="money",n.numeric="numeric",n.oid="oid",n.reltime="reltime",n.text="text",n.time="time",n.timestamp="timestamp",n.timestamptz="timestamptz",n.timetz="timetz",n.tsrange="tsrange",n.tstzrange="tstzrange"})(T||(T={}));const St=(n,e,t={})=>{var s;const r=(s=t.skipTypes)!==null&&s!==void 0?s:[];return Object.keys(e).reduce((i,o)=>(i[o]=Rs(o,n,e,r),i),{})},Rs=(n,e,t,s)=>{const r=e.find(a=>a.name===n),i=r==null?void 0:r.type,o=t[n];return i&&!s.includes(i)?Jt(i,o):ze(o)},Jt=(n,e)=>{if(n.charAt(0)==="_"){const t=n.slice(1,n.length);return Us(e,t)}switch(n){case T.bool:return Is(e);case T.float4:case T.float8:case T.int2:case T.int4:case T.int8:case T.numeric:case T.oid:return Cs(e);case T.json:case T.jsonb:return xs(e);case T.timestamp:return Ls(e);case T.abstime:case T.date:case T.daterange:case T.int4range:case T.int8range:case T.money:case T.reltime:case T.text:case T.time:case T.timestamptz:case T.timetz:case T.tsrange:case T.tstzrange:return ze(e);default:return ze(e)}},ze=n=>n,Is=n=>{switch(n){case"t":return!0;case"f":return!1;default:return n}},Cs=n=>{if(typeof n=="string"){const e=parseFloat(n);if(!Number.isNaN(e))return e}return n},xs=n=>{if(typeof n=="string")try{return JSON.parse(n)}catch(e){return n}return n},Us=(n,e)=>{if(typeof n!="string")return n;const t=n.length-1,s=n[t];if(n[0]==="{"&&s==="}"){let i;const o=n.slice(1,t);try{i=JSON.parse("["+o+"]")}catch(a){i=o?o.split(","):[]}return i.map(a=>Jt(e,a))}return n},Ls=n=>typeof n=="string"?n.replace(" ","T"):n,zt=n=>{let e=n;return e=e.replace(/^ws/i,"http"),e=e.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),e.replace(/\/+$/,"")};class De{constructor(e,t,s={},r=Wt){this.channel=e,this.event=t,this.payload=s,this.timeout=r,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var s;return this._hasReceived(e)&&t((s=this.receivedResp)===null||s===void 0?void 0:s.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const e=t=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=t,this._matchReceive(t)};this.channel._on(this.refEvent,{},e),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(s=>s.status===e).forEach(s=>s.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var Et;(function(n){n.SYNC="sync",n.JOIN="join",n.LEAVE="leave"})(Et||(Et={}));class ye{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const s=(t==null?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(s.state,{},r=>{const{onJoin:i,onLeave:o,onSync:a}=this.caller;this.joinRef=this.channel._joinRef(),this.state=ye.syncState(this.state,r,i,o),this.pendingDiffs.forEach(l=>{this.state=ye.syncDiff(this.state,l,i,o)}),this.pendingDiffs=[],a()}),this.channel._on(s.diff,{},r=>{const{onJoin:i,onLeave:o,onSync:a}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(r):(this.state=ye.syncDiff(this.state,r,i,o),a())}),this.onJoin((r,i,o)=>{this.channel._trigger("presence",{event:"join",key:r,currentPresences:i,newPresences:o})}),this.onLeave((r,i,o)=>{this.channel._trigger("presence",{event:"leave",key:r,currentPresences:i,leftPresences:o})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,s,r){const i=this.cloneDeep(e),o=this.transformState(t),a={},l={};return this.map(i,(c,u)=>{o[c]||(l[c]=u)}),this.map(o,(c,u)=>{const d=i[c];if(d){const f=u.map(_=>_.presence_ref),g=d.map(_=>_.presence_ref),p=u.filter(_=>g.indexOf(_.presence_ref)<0),w=d.filter(_=>f.indexOf(_.presence_ref)<0);p.length>0&&(a[c]=p),w.length>0&&(l[c]=w)}else a[c]=u}),this.syncDiff(i,{joins:a,leaves:l},s,r)}static syncDiff(e,t,s,r){const{joins:i,leaves:o}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return s||(s=()=>{}),r||(r=()=>{}),this.map(i,(a,l)=>{var c;const u=(c=e[a])!==null&&c!==void 0?c:[];if(e[a]=this.cloneDeep(l),u.length>0){const d=e[a].map(g=>g.presence_ref),f=u.filter(g=>d.indexOf(g.presence_ref)<0);e[a].unshift(...f)}s(a,u,l)}),this.map(o,(a,l)=>{let c=e[a];if(!c)return;const u=l.map(d=>d.presence_ref);c=c.filter(d=>u.indexOf(d.presence_ref)<0),e[a]=c,r(a,c,l),c.length===0&&delete e[a]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(s=>t(s,e[s]))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce((t,s)=>{const r=e[s];return"metas"in r?t[s]=r.metas.map(i=>(i.presence_ref=i.phx_ref,delete i.phx_ref,delete i.phx_ref_prev,i)):t[s]=r,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var Pt;(function(n){n.ALL="*",n.INSERT="INSERT",n.UPDATE="UPDATE",n.DELETE="DELETE"})(Pt||(Pt={}));var jt;(function(n){n.BROADCAST="broadcast",n.PRESENCE="presence",n.POSTGRES_CHANGES="postgres_changes",n.SYSTEM="system"})(jt||(jt={}));var N;(function(n){n.SUBSCRIBED="SUBSCRIBED",n.TIMED_OUT="TIMED_OUT",n.CLOSED="CLOSED",n.CHANNEL_ERROR="CHANNEL_ERROR"})(N||(N={}));class st{constructor(e,t={config:{}},s){this.topic=e,this.params=t,this.socket=s,this.bindings={},this.state=U.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new De(this,q.join,this.params,this.timeout),this.rejoinTimer=new Kt(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=U.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(r=>r.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=U.closed,this.socket._remove(this)}),this._onError(r=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,r),this.state=U.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=U.errored,this.rejoinTimer.scheduleTimeout())}),this._on(q.reply,{},(r,i)=>{this._trigger(this._replyEventName(i),r)}),this.presence=new ye(this),this.broadcastEndpointURL=zt(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var s,r;if(this.socket.isConnected()||this.socket.connect(),this.state==U.closed){const{config:{broadcast:i,presence:o,private:a}}=this.params;this._onError(u=>e==null?void 0:e(N.CHANNEL_ERROR,u)),this._onClose(()=>e==null?void 0:e(N.CLOSED));const l={},c={broadcast:i,presence:o,postgres_changes:(r=(s=this.bindings.postgres_changes)===null||s===void 0?void 0:s.map(u=>u.filter))!==null&&r!==void 0?r:[],private:a};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:c},l)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",d=>h(this,[d],function*({postgres_changes:u}){var f;if(this.socket.setAuth(),u===void 0){e==null||e(N.SUBSCRIBED);return}else{const g=this.bindings.postgres_changes,p=(f=g==null?void 0:g.length)!==null&&f!==void 0?f:0,w=[];for(let _=0;_<p;_++){const j=g[_],{filter:{event:A,schema:y,table:E,filter:O}}=j,P=u&&u[_];if(P&&P.event===A&&P.schema===y&&P.table===E&&P.filter===O)w.push(Object.assign(Object.assign({},j),{id:P.id}));else{this.unsubscribe(),this.state=U.errored,e==null||e(N.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=w,e&&e(N.SUBSCRIBED);return}})).receive("error",u=>{this.state=U.errored,e==null||e(N.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(u).join(", ")||"error")))}).receive("timeout",()=>{e==null||e(N.TIMED_OUT)})}return this}presenceState(){return this.presence.state}track(s){return h(this,arguments,function*(e,t={}){return yield this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)})}untrack(){return h(this,arguments,function*(e={}){return yield this.send({type:"presence",event:"untrack"},e)})}on(e,t,s){return this._on(e,t,s)}send(s){return h(this,arguments,function*(e,t={}){var r,i;if(!this._canPush()&&e.type==="broadcast"){const{event:o,payload:a}=e,c={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:o,payload:a,private:this.private}]})};try{const u=yield this._fetchWithTimeout(this.broadcastEndpointURL,c,(r=t.timeout)!==null&&r!==void 0?r:this.timeout);return yield(i=u.body)===null||i===void 0?void 0:i.cancel(),u.ok?"ok":"error"}catch(u){return u.name==="AbortError"?"timed out":"error"}}else return new Promise(o=>{var a,l,c;const u=this._push(e.type,e,t.timeout||this.timeout);e.type==="broadcast"&&!(!((c=(l=(a=this.params)===null||a===void 0?void 0:a.config)===null||l===void 0?void 0:l.broadcast)===null||c===void 0)&&c.ack)&&o("ok"),u.receive("ok",()=>o("ok")),u.receive("error",()=>o("error")),u.receive("timeout",()=>o("timed out"))})})}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=U.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(q.close,"leave",this._joinRef())};this.joinPush.destroy();let s=null;return new Promise(r=>{s=new De(this,q.leave,{},e),s.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),s.send(),this._canPush()||s.trigger("ok",{})}).finally(()=>{s==null||s.destroy()})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}_fetchWithTimeout(e,t,s){return h(this,null,function*(){const r=new AbortController,i=setTimeout(()=>r.abort(),s),o=yield this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:r.signal}));return clearTimeout(i),o})}_push(e,t,s=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let r=new De(this,e,t,s);return this._canPush()?r.send():(r.startTimeout(),this.pushBuffer.push(r)),r}_onMessage(e,t,s){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,s){var r,i;const o=e.toLocaleLowerCase(),{close:a,error:l,leave:c,join:u}=q;if(s&&[a,l,c,u].indexOf(o)>=0&&s!==this._joinRef())return;let f=this._onMessage(o,t,s);if(t&&!f)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?(r=this.bindings.postgres_changes)===null||r===void 0||r.filter(g=>{var p,w,_;return((p=g.filter)===null||p===void 0?void 0:p.event)==="*"||((_=(w=g.filter)===null||w===void 0?void 0:w.event)===null||_===void 0?void 0:_.toLocaleLowerCase())===o}).map(g=>g.callback(f,s)):(i=this.bindings[o])===null||i===void 0||i.filter(g=>{var p,w,_,j,A,y;if(["broadcast","presence","postgres_changes"].includes(o))if("id"in g){const E=g.id,O=(p=g.filter)===null||p===void 0?void 0:p.event;return E&&((w=t.ids)===null||w===void 0?void 0:w.includes(E))&&(O==="*"||(O==null?void 0:O.toLocaleLowerCase())===((_=t.data)===null||_===void 0?void 0:_.type.toLocaleLowerCase()))}else{const E=(A=(j=g==null?void 0:g.filter)===null||j===void 0?void 0:j.event)===null||A===void 0?void 0:A.toLocaleLowerCase();return E==="*"||E===((y=t==null?void 0:t.event)===null||y===void 0?void 0:y.toLocaleLowerCase())}else return g.type.toLocaleLowerCase()===o}).map(g=>{if(typeof f=="object"&&"ids"in f){const p=f.data,{schema:w,table:_,commit_timestamp:j,type:A,errors:y}=p;f=Object.assign(Object.assign({},{schema:w,table:_,commit_timestamp:j,eventType:A,new:{},old:{},errors:y}),this._getPayloadRecords(p))}g.callback(f,s)})}_isClosed(){return this.state===U.closed}_isJoined(){return this.state===U.joined}_isJoining(){return this.state===U.joining}_isLeaving(){return this.state===U.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,s){const r=e.toLocaleLowerCase(),i={type:r,filter:t,callback:s};return this.bindings[r]?this.bindings[r].push(i):this.bindings[r]=[i],this}_off(e,t){const s=e.toLocaleLowerCase();return this.bindings[s]=this.bindings[s].filter(r=>{var i;return!(((i=r.type)===null||i===void 0?void 0:i.toLocaleLowerCase())===s&&st.isEqual(r.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e[s]!==t[s])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(q.close,{},e)}_onError(e){this._on(q.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=U.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return(e.type==="INSERT"||e.type==="UPDATE")&&(t.new=St(e.columns,e.record)),(e.type==="UPDATE"||e.type==="DELETE")&&(t.old=St(e.columns,e.old_record)),t}}const Tt=()=>{},Ds=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class Yr{constructor(e,t){var s;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=Wt,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=Tt,this.ref=0,this.logger=Tt,this.conn=null,this.sendBuffer=[],this.serializer=new As,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=i=>{let o;return i?o=i:typeof fetch=="undefined"?o=(...a)=>me(()=>h(this,null,function*(){const{default:l}=yield Promise.resolve().then(()=>de);return{default:l}}),void 0).then(({default:l})=>l(...a)):o=fetch,(...a)=>o(...a)},this.endPoint=`${e}/${Je.websocket}`,this.httpEndpoint=zt(e),t!=null&&t.transport?this.transport=t.transport:this.transport=null,t!=null&&t.params&&(this.params=t.params),t!=null&&t.timeout&&(this.timeout=t.timeout),t!=null&&t.logger&&(this.logger=t.logger),(t!=null&&t.logLevel||t!=null&&t.log_level)&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),t!=null&&t.heartbeatIntervalMs&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);const r=(s=t==null?void 0:t.params)===null||s===void 0?void 0:s.apikey;if(r&&(this.accessTokenValue=r,this.apiKey=r),this.reconnectAfterMs=t!=null&&t.reconnectAfterMs?t.reconnectAfterMs:i=>[1e3,2e3,5e3,1e4][i-1]||1e4,this.encode=t!=null&&t.encode?t.encode:(i,o)=>o(JSON.stringify(i)),this.decode=t!=null&&t.decode?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new Kt(()=>h(this,null,function*(){this.disconnect(),this.connect()}),this.reconnectAfterMs),this.fetch=this._resolveFetch(t==null?void 0:t.fetch),t!=null&&t.worker){if(typeof window!="undefined"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(t==null?void 0:t.worker)||!1,this.workerUrl=t==null?void 0:t.workerUrl}this.accessToken=(t==null?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=Ps),!this.transport)throw new Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:$s}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,t!=null?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(s=>s.teardown()))}getChannels(){return this.channels}removeChannel(e){return h(this,null,function*(){const t=yield e.unsubscribe();return this.channels.length===0&&this.disconnect(),t})}removeAllChannels(){return h(this,null,function*(){const e=yield Promise.all(this.channels.map(t=>t.unsubscribe()));return this.channels=[],this.disconnect(),e})}log(e,t,s){this.logger(e,t,s)}connectionState(){switch(this.conn&&this.conn.readyState){case _e.connecting:return Y.Connecting;case _e.open:return Y.Open;case _e.closing:return Y.Closing;default:return Y.Closed}}isConnected(){return this.connectionState()===Y.Open}channel(e,t={config:{}}){const s=`realtime:${e}`,r=this.getChannels().find(i=>i.topic===s);if(r)return r;{const i=new st(`realtime:${e}`,t,this);return this.channels.push(i),i}}push(e){const{topic:t,event:s,payload:r,ref:i}=e,o=()=>{this.encode(e,a=>{var l;(l=this.conn)===null||l===void 0||l.send(a)})};this.log("push",`${t} ${s} (${i})`,r),this.isConnected()?o():this.sendBuffer.push(o)}setAuth(e=null){return h(this,null,function*(){let t=e||this.accessToken&&(yield this.accessToken())||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(s=>{const r={access_token:t,version:Ts};t&&s.updateJoinPayload(r),s.joinedOnce&&s._isJoined()&&s._push(q.access_token,{access_token:t})}))})}sendHeartbeat(){return h(this,null,function*(){var e;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),(e=this.conn)===null||e===void 0||e.close(Os,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),yield this.setAuth()})}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(s=>s.topic===e&&(s._isJoined()||s._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,t=>{let{topic:s,event:r,payload:i,ref:o}=t;s==="phoenix"&&r==="phx_reply"&&this.heartbeatCallback(t.payload.status=="ok"?"ok":"error"),o&&o===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${i.status||""} ${s} ${r} ${o&&"("+o+")"||""}`,i),Array.from(this.channels).filter(a=>a._isMember(s)).forEach(a=>a._trigger(r,i,o)),this.stateChangeCallbacks.message.forEach(a=>a(t))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(e=>e())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=t=>{this.log("worker","worker error",t.message),this.workerRef.terminate()},this.workerRef.onmessage=t=>{t.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",`${e}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(q.error))}_appendParams(e,t){if(Object.keys(t).length===0)return e;const s=e.match(/\?/)?"&":"?",r=new URLSearchParams(t);return`${e}${s}${r}`}_workerObjectUrl(e){let t;if(e)t=e;else{const s=new Blob([Ds],{type:"application/javascript"});t=URL.createObjectURL(s)}return t}}class rt extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function x(n){return typeof n=="object"&&n!==null&&"__isStorageError"in n}class qs extends rt{constructor(e,t,s){super(e),this.name="StorageApiError",this.status=t,this.statusCode=s}toJSON(){return{name:this.name,message:this.message,status:this.status,statusCode:this.statusCode}}}class He extends rt{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var Bs=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(o){o(i)})}return new(t||(t=Promise))(function(i,o){function a(u){try{c(s.next(u))}catch(d){o(d)}}function l(u){try{c(s.throw(u))}catch(d){o(d)}}function c(u){u.done?i(u.value):r(u.value).then(a,l)}c((s=s.apply(n,e||[])).next())})};const Ht=n=>{let e;return n?e=n:typeof fetch=="undefined"?e=(...t)=>me(()=>h(null,null,function*(){const{default:s}=yield Promise.resolve().then(()=>de);return{default:s}}),void 0).then(({default:s})=>s(...t)):e=fetch,(...t)=>e(...t)},Ns=()=>Bs(void 0,void 0,void 0,function*(){return typeof Response=="undefined"?(yield me(()=>Promise.resolve().then(()=>de),void 0)).Response:Response}),Ge=n=>{if(Array.isArray(n))return n.map(t=>Ge(t));if(typeof n=="function"||n!==Object(n))return n;const e={};return Object.entries(n).forEach(([t,s])=>{const r=t.replace(/([-_][a-z])/gi,i=>i.toUpperCase().replace(/[-_]/g,""));e[r]=Ge(s)}),e},Ms=n=>{if(typeof n!="object"||n===null)return!1;const e=Object.getPrototypeOf(n);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Symbol.toStringTag in n)&&!(Symbol.iterator in n)};var X=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(o){o(i)})}return new(t||(t=Promise))(function(i,o){function a(u){try{c(s.next(u))}catch(d){o(d)}}function l(u){try{c(s.throw(u))}catch(d){o(d)}}function c(u){u.done?i(u.value):r(u.value).then(a,l)}c((s=s.apply(n,e||[])).next())})};const qe=n=>n.msg||n.message||n.error_description||n.error||JSON.stringify(n),Fs=(n,e,t)=>X(void 0,void 0,void 0,function*(){const s=yield Ns();n instanceof s&&!(t!=null&&t.noResolveJson)?n.json().then(r=>{const i=n.status||500,o=(r==null?void 0:r.statusCode)||i+"";e(new qs(qe(r),i,o))}).catch(r=>{e(new He(qe(r),r))}):e(new He(qe(n),n))}),Ws=(n,e,t,s)=>{const r={method:n,headers:(e==null?void 0:e.headers)||{}};return n==="GET"||!s?r:(Ms(s)?(r.headers=Object.assign({"Content-Type":"application/json"},e==null?void 0:e.headers),r.body=JSON.stringify(s)):r.body=s,Object.assign(Object.assign({},r),t))};function be(n,e,t,s,r,i){return X(this,void 0,void 0,function*(){return new Promise((o,a)=>{n(t,Ws(e,s,r,i)).then(l=>{if(!l.ok)throw l;return s!=null&&s.noResolveJson?l:l.json()}).then(l=>o(l)).catch(l=>Fs(l,a,s))})})}function $e(n,e,t,s){return X(this,void 0,void 0,function*(){return be(n,"GET",e,t,s)})}function M(n,e,t,s,r){return X(this,void 0,void 0,function*(){return be(n,"POST",e,s,r,t)})}function Ve(n,e,t,s,r){return X(this,void 0,void 0,function*(){return be(n,"PUT",e,s,r,t)})}function Ks(n,e,t,s){return X(this,void 0,void 0,function*(){return be(n,"HEAD",e,Object.assign(Object.assign({},t),{noResolveJson:!0}),s)})}function Gt(n,e,t,s,r){return X(this,void 0,void 0,function*(){return be(n,"DELETE",e,s,r,t)})}var L=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(o){o(i)})}return new(t||(t=Promise))(function(i,o){function a(u){try{c(s.next(u))}catch(d){o(d)}}function l(u){try{c(s.throw(u))}catch(d){o(d)}}function c(u){u.done?i(u.value):r(u.value).then(a,l)}c((s=s.apply(n,e||[])).next())})};const Js={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},$t={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class zs{constructor(e,t={},s,r){this.url=e,this.headers=t,this.bucketId=s,this.fetch=Ht(r)}uploadOrUpdate(e,t,s,r){return L(this,void 0,void 0,function*(){try{let i;const o=Object.assign(Object.assign({},$t),r);let a=Object.assign(Object.assign({},this.headers),e==="POST"&&{"x-upsert":String(o.upsert)});const l=o.metadata;typeof Blob!="undefined"&&s instanceof Blob?(i=new FormData,i.append("cacheControl",o.cacheControl),l&&i.append("metadata",this.encodeMetadata(l)),i.append("",s)):typeof FormData!="undefined"&&s instanceof FormData?(i=s,i.append("cacheControl",o.cacheControl),l&&i.append("metadata",this.encodeMetadata(l))):(i=s,a["cache-control"]=`max-age=${o.cacheControl}`,a["content-type"]=o.contentType,l&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(l)))),r!=null&&r.headers&&(a=Object.assign(Object.assign({},a),r.headers));const c=this._removeEmptyFolders(t),u=this._getFinalPath(c),d=yield(e=="PUT"?Ve:M)(this.fetch,`${this.url}/object/${u}`,i,Object.assign({headers:a},o!=null&&o.duplex?{duplex:o.duplex}:{}));return{data:{path:c,id:d.Id,fullPath:d.Key},error:null}}catch(i){if(x(i))return{data:null,error:i};throw i}})}upload(e,t,s){return L(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,s)})}uploadToSignedUrl(e,t,s,r){return L(this,void 0,void 0,function*(){const i=this._removeEmptyFolders(e),o=this._getFinalPath(i),a=new URL(this.url+`/object/upload/sign/${o}`);a.searchParams.set("token",t);try{let l;const c=Object.assign({upsert:$t.upsert},r),u=Object.assign(Object.assign({},this.headers),{"x-upsert":String(c.upsert)});typeof Blob!="undefined"&&s instanceof Blob?(l=new FormData,l.append("cacheControl",c.cacheControl),l.append("",s)):typeof FormData!="undefined"&&s instanceof FormData?(l=s,l.append("cacheControl",c.cacheControl)):(l=s,u["cache-control"]=`max-age=${c.cacheControl}`,u["content-type"]=c.contentType);const d=yield Ve(this.fetch,a.toString(),l,{headers:u});return{data:{path:i,fullPath:d.Key},error:null}}catch(l){if(x(l))return{data:null,error:l};throw l}})}createSignedUploadUrl(e,t){return L(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e);const r=Object.assign({},this.headers);t!=null&&t.upsert&&(r["x-upsert"]="true");const i=yield M(this.fetch,`${this.url}/object/upload/sign/${s}`,{},{headers:r}),o=new URL(this.url+i.url),a=o.searchParams.get("token");if(!a)throw new rt("No token returned by API");return{data:{signedUrl:o.toString(),path:e,token:a},error:null}}catch(s){if(x(s))return{data:null,error:s};throw s}})}update(e,t,s){return L(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,s)})}move(e,t,s){return L(this,void 0,void 0,function*(){try{return{data:yield M(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:s==null?void 0:s.destinationBucket},{headers:this.headers}),error:null}}catch(r){if(x(r))return{data:null,error:r};throw r}})}copy(e,t,s){return L(this,void 0,void 0,function*(){try{return{data:{path:(yield M(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:s==null?void 0:s.destinationBucket},{headers:this.headers})).Key},error:null}}catch(r){if(x(r))return{data:null,error:r};throw r}})}createSignedUrl(e,t,s){return L(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),i=yield M(this.fetch,`${this.url}/object/sign/${r}`,Object.assign({expiresIn:t},s!=null&&s.transform?{transform:s.transform}:{}),{headers:this.headers});const o=s!=null&&s.download?`&download=${s.download===!0?"":s.download}`:"";return i={signedUrl:encodeURI(`${this.url}${i.signedURL}${o}`)},{data:i,error:null}}catch(r){if(x(r))return{data:null,error:r};throw r}})}createSignedUrls(e,t,s){return L(this,void 0,void 0,function*(){try{const r=yield M(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),i=s!=null&&s.download?`&download=${s.download===!0?"":s.download}`:"";return{data:r.map(o=>Object.assign(Object.assign({},o),{signedUrl:o.signedURL?encodeURI(`${this.url}${o.signedURL}${i}`):null})),error:null}}catch(r){if(x(r))return{data:null,error:r};throw r}})}download(e,t){return L(this,void 0,void 0,function*(){const r=typeof(t==null?void 0:t.transform)!="undefined"?"render/image/authenticated":"object",i=this.transformOptsToQueryString((t==null?void 0:t.transform)||{}),o=i?`?${i}`:"";try{const a=this._getFinalPath(e);return{data:yield(yield $e(this.fetch,`${this.url}/${r}/${a}${o}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(a){if(x(a))return{data:null,error:a};throw a}})}info(e){return L(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{const s=yield $e(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:Ge(s),error:null}}catch(s){if(x(s))return{data:null,error:s};throw s}})}exists(e){return L(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{return yield Ks(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(s){if(x(s)&&s instanceof He){const r=s.originalError;if([400,404].includes(r==null?void 0:r.status))return{data:!1,error:s}}throw s}})}getPublicUrl(e,t){const s=this._getFinalPath(e),r=[],i=t!=null&&t.download?`download=${t.download===!0?"":t.download}`:"";i!==""&&r.push(i);const a=typeof(t==null?void 0:t.transform)!="undefined"?"render/image":"object",l=this.transformOptsToQueryString((t==null?void 0:t.transform)||{});l!==""&&r.push(l);let c=r.join("&");return c!==""&&(c=`?${c}`),{data:{publicUrl:encodeURI(`${this.url}/${a}/public/${s}${c}`)}}}remove(e){return L(this,void 0,void 0,function*(){try{return{data:yield Gt(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(x(t))return{data:null,error:t};throw t}})}list(e,t,s){return L(this,void 0,void 0,function*(){try{const r=Object.assign(Object.assign(Object.assign({},Js),t),{prefix:e||""});return{data:yield M(this.fetch,`${this.url}/object/list/${this.bucketId}`,r,{headers:this.headers},s),error:null}}catch(r){if(x(r))return{data:null,error:r};throw r}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return typeof Buffer!="undefined"?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e.replace(/^\/+/,"")}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const Hs="2.10.4",Gs={"X-Client-Info":`storage-js/${Hs}`};var ie=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(o){o(i)})}return new(t||(t=Promise))(function(i,o){function a(u){try{c(s.next(u))}catch(d){o(d)}}function l(u){try{c(s.throw(u))}catch(d){o(d)}}function c(u){u.done?i(u.value):r(u.value).then(a,l)}c((s=s.apply(n,e||[])).next())})};class Vs{constructor(e,t={},s,r){const i=new URL(e);r!=null&&r.useNewHostname&&/supabase\.(co|in|red)$/.test(i.hostname)&&!i.hostname.includes("storage.supabase.")&&(i.hostname=i.hostname.replace("supabase.","storage.supabase.")),this.url=i.href,this.headers=Object.assign(Object.assign({},Gs),t),this.fetch=Ht(s)}listBuckets(){return ie(this,void 0,void 0,function*(){try{return{data:yield $e(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(x(e))return{data:null,error:e};throw e}})}getBucket(e){return ie(this,void 0,void 0,function*(){try{return{data:yield $e(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(x(t))return{data:null,error:t};throw t}})}createBucket(e,t={public:!1}){return ie(this,void 0,void 0,function*(){try{return{data:yield M(this.fetch,`${this.url}/bucket`,{id:e,name:e,type:t.type,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(s){if(x(s))return{data:null,error:s};throw s}})}updateBucket(e,t){return ie(this,void 0,void 0,function*(){try{return{data:yield Ve(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(s){if(x(s))return{data:null,error:s};throw s}})}emptyBucket(e){return ie(this,void 0,void 0,function*(){try{return{data:yield M(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(x(t))return{data:null,error:t};throw t}})}deleteBucket(e){return ie(this,void 0,void 0,function*(){try{return{data:yield Gt(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(x(t))return{data:null,error:t};throw t}})}}class Xr extends Vs{constructor(e,t={},s,r){super(e,t,s,r)}from(e){return new zs(this.url,this.headers,e,this.fetch)}}const Vt="2.71.1",ce=30*1e3,Qe=3,Be=Qe*ce,Qs="http://localhost:9999",Ys="supabase.auth.token",Xs={"X-Client-Info":`gotrue-js/${Vt}`},Ye="X-Supabase-Api-Version",Qt={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},Zs=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,er=600*1e3;class it extends Error{constructor(e,t,s){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=s}}function m(n){return typeof n=="object"&&n!==null&&"__isAuthError"in n}class tr extends it{constructor(e,t,s){super(e,t,s),this.name="AuthApiError",this.status=t,this.code=s}}function sr(n){return m(n)&&n.name==="AuthApiError"}class Yt extends it{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class J extends it{constructor(e,t,s,r){super(e,s,r),this.name=t,this.status=s}}class W extends J{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function rr(n){return m(n)&&n.name==="AuthSessionMissingError"}class Pe extends J{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class je extends J{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class Te extends J{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function ir(n){return m(n)&&n.name==="AuthImplicitGrantRedirectError"}class Ot extends J{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Xe extends J{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function Ne(n){return m(n)&&n.name==="AuthRetryableFetchError"}class At extends J{constructor(e,t,s){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=s}}class Ze extends J{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const Oe="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),Rt=` 	
\r=`.split(""),nr=(()=>{const n=new Array(128);for(let e=0;e<n.length;e+=1)n[e]=-1;for(let e=0;e<Rt.length;e+=1)n[Rt[e].charCodeAt(0)]=-2;for(let e=0;e<Oe.length;e+=1)n[Oe[e].charCodeAt(0)]=e;return n})();function It(n,e,t){if(n!==null)for(e.queue=e.queue<<8|n,e.queuedBits+=8;e.queuedBits>=6;){const s=e.queue>>e.queuedBits-6&63;t(Oe[s]),e.queuedBits-=6}else if(e.queuedBits>0)for(e.queue=e.queue<<6-e.queuedBits,e.queuedBits=6;e.queuedBits>=6;){const s=e.queue>>e.queuedBits-6&63;t(Oe[s]),e.queuedBits-=6}}function Xt(n,e,t){const s=nr[n];if(s>-1)for(e.queue=e.queue<<6|s,e.queuedBits+=6;e.queuedBits>=8;)t(e.queue>>e.queuedBits-8&255),e.queuedBits-=8;else{if(s===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(n)}"`)}}function Ct(n){const e=[],t=o=>{e.push(String.fromCodePoint(o))},s={utf8seq:0,codepoint:0},r={queue:0,queuedBits:0},i=o=>{lr(o,s,t)};for(let o=0;o<n.length;o+=1)Xt(n.charCodeAt(o),r,i);return e.join("")}function or(n,e){if(n<=127){e(n);return}else if(n<=2047){e(192|n>>6),e(128|n&63);return}else if(n<=65535){e(224|n>>12),e(128|n>>6&63),e(128|n&63);return}else if(n<=1114111){e(240|n>>18),e(128|n>>12&63),e(128|n>>6&63),e(128|n&63);return}throw new Error(`Unrecognized Unicode codepoint: ${n.toString(16)}`)}function ar(n,e){for(let t=0;t<n.length;t+=1){let s=n.charCodeAt(t);if(s>55295&&s<=56319){const r=(s-55296)*1024&65535;s=(n.charCodeAt(t+1)-56320&65535|r)+65536,t+=1}or(s,e)}}function lr(n,e,t){if(e.utf8seq===0){if(n<=127){t(n);return}for(let s=1;s<6;s+=1)if((n>>7-s&1)===0){e.utf8seq=s;break}if(e.utf8seq===2)e.codepoint=n&31;else if(e.utf8seq===3)e.codepoint=n&15;else if(e.utf8seq===4)e.codepoint=n&7;else throw new Error("Invalid UTF-8 sequence");e.utf8seq-=1}else if(e.utf8seq>0){if(n<=127)throw new Error("Invalid UTF-8 sequence");e.codepoint=e.codepoint<<6|n&63,e.utf8seq-=1,e.utf8seq===0&&t(e.codepoint)}}function cr(n){const e=[],t={queue:0,queuedBits:0},s=r=>{e.push(r)};for(let r=0;r<n.length;r+=1)Xt(n.charCodeAt(r),t,s);return new Uint8Array(e)}function ur(n){const e=[];return ar(n,t=>e.push(t)),new Uint8Array(e)}function hr(n){const e=[],t={queue:0,queuedBits:0},s=r=>{e.push(r)};return n.forEach(r=>It(r,t,s)),It(null,t,s),e.join("")}function dr(n){return Math.round(Date.now()/1e3)+n}function fr(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(n){const e=Math.random()*16|0;return(n=="x"?e:e&3|8).toString(16)})}const D=()=>typeof window!="undefined"&&typeof document!="undefined",G={tested:!1,writable:!1},Zt=()=>{if(!D())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch(e){return!1}if(G.tested)return G.writable;const n=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(n,n),globalThis.localStorage.removeItem(n),G.tested=!0,G.writable=!0}catch(e){G.tested=!0,G.writable=!1}return G.writable};function gr(n){const e={},t=new URL(n);if(t.hash&&t.hash[0]==="#")try{new URLSearchParams(t.hash.substring(1)).forEach((r,i)=>{e[i]=r})}catch(s){}return t.searchParams.forEach((s,r)=>{e[r]=s}),e}const es=n=>{let e;return n?e=n:typeof fetch=="undefined"?e=(...t)=>me(()=>h(null,null,function*(){const{default:s}=yield Promise.resolve().then(()=>de);return{default:s}}),void 0).then(({default:s})=>s(...t)):e=fetch,(...t)=>e(...t)},pr=n=>typeof n=="object"&&n!==null&&"status"in n&&"ok"in n&&"json"in n&&typeof n.json=="function",ue=(n,e,t)=>h(null,null,function*(){yield n.setItem(e,JSON.stringify(t))}),V=(n,e)=>h(null,null,function*(){const t=yield n.getItem(e);if(!t)return null;try{return JSON.parse(t)}catch(s){return t}}),F=(n,e)=>h(null,null,function*(){yield n.removeItem(e)});class Ae{constructor(){this.promise=new Ae.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}Ae.promiseConstructor=Promise;function Me(n){const e=n.split(".");if(e.length!==3)throw new Ze("Invalid JWT structure");for(let s=0;s<e.length;s++)if(!Zs.test(e[s]))throw new Ze("JWT not in base64url format");return{header:JSON.parse(Ct(e[0])),payload:JSON.parse(Ct(e[1])),signature:cr(e[2]),raw:{header:e[0],payload:e[1]}}}function vr(n){return h(this,null,function*(){return yield new Promise(e=>{setTimeout(()=>e(null),n)})})}function _r(n,e){return new Promise((s,r)=>{h(null,null,function*(){for(let i=0;i<1/0;i++)try{const o=yield n(i);if(!e(i,null,o)){s(o);return}}catch(o){if(!e(i,o)){r(o);return}}})})}function yr(n){return("0"+n.toString(16)).substr(-2)}function wr(){const e=new Uint32Array(56);if(typeof crypto=="undefined"){const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",s=t.length;let r="";for(let i=0;i<56;i++)r+=t.charAt(Math.floor(Math.random()*s));return r}return crypto.getRandomValues(e),Array.from(e,yr).join("")}function mr(n){return h(this,null,function*(){const t=new TextEncoder().encode(n),s=yield crypto.subtle.digest("SHA-256",t),r=new Uint8Array(s);return Array.from(r).map(i=>String.fromCharCode(i)).join("")})}function br(n){return h(this,null,function*(){if(!(typeof crypto!="undefined"&&typeof crypto.subtle!="undefined"&&typeof TextEncoder!="undefined"))return n;const t=yield mr(n);return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")})}function ne(n,e,t=!1){return h(this,null,function*(){const s=wr();let r=s;t&&(r+="/PASSWORD_RECOVERY"),yield ue(n,`${e}-code-verifier`,r);const i=yield br(s);return[i,s===i?"plain":"s256"]})}const kr=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function Sr(n){const e=n.headers.get(Ye);if(!e||!e.match(kr))return null;try{return new Date(`${e}T00:00:00.0Z`)}catch(t){return null}}function Er(n){if(!n)throw new Error("Missing exp claim");const e=Math.floor(Date.now()/1e3);if(n<=e)throw new Error("JWT has expired")}function Pr(n){switch(n){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}const jr=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function oe(n){if(!jr.test(n))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}function Fe(){const n={};return new Proxy(n,{get:(e,t)=>{if(t==="__isUserNotAvailableProxy")return!0;if(typeof t=="symbol"){const s=t.toString();if(s==="Symbol(Symbol.toPrimitive)"||s==="Symbol(Symbol.toStringTag)"||s==="Symbol(util.inspect.custom)")return}throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Accessing the "${t}" property of the session object is not supported. Please use getUser() instead.`)},set:(e,t)=>{throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Setting the "${t}" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`)},deleteProperty:(e,t)=>{throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Deleting the "${t}" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`)}})}function xt(n){return JSON.parse(JSON.stringify(n))}var Tr=function(n,e){var t={};for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&e.indexOf(s)<0&&(t[s]=n[s]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,s=Object.getOwnPropertySymbols(n);r<s.length;r++)e.indexOf(s[r])<0&&Object.prototype.propertyIsEnumerable.call(n,s[r])&&(t[s[r]]=n[s[r]]);return t};const Q=n=>n.msg||n.message||n.error_description||n.error||JSON.stringify(n),$r=[502,503,504];function Ut(n){return h(this,null,function*(){var e;if(!pr(n))throw new Xe(Q(n),0);if($r.includes(n.status))throw new Xe(Q(n),n.status);let t;try{t=yield n.json()}catch(i){throw new Yt(Q(i),i)}let s;const r=Sr(n);if(r&&r.getTime()>=Qt["2024-01-01"].timestamp&&typeof t=="object"&&t&&typeof t.code=="string"?s=t.code:typeof t=="object"&&t&&typeof t.error_code=="string"&&(s=t.error_code),s){if(s==="weak_password")throw new At(Q(t),n.status,((e=t.weak_password)===null||e===void 0?void 0:e.reasons)||[]);if(s==="session_not_found")throw new W}else if(typeof t=="object"&&t&&typeof t.weak_password=="object"&&t.weak_password&&Array.isArray(t.weak_password.reasons)&&t.weak_password.reasons.length&&t.weak_password.reasons.reduce((i,o)=>i&&typeof o=="string",!0))throw new At(Q(t),n.status,t.weak_password.reasons);throw new tr(Q(t),n.status||500,s)})}const Or=(n,e,t,s)=>{const r={method:n,headers:(e==null?void 0:e.headers)||{}};return n==="GET"?r:(r.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},e==null?void 0:e.headers),r.body=JSON.stringify(s),Object.assign(Object.assign({},r),t))};function S(n,e,t,s){return h(this,null,function*(){var r;const i=Object.assign({},s==null?void 0:s.headers);i[Ye]||(i[Ye]=Qt["2024-01-01"].name),s!=null&&s.jwt&&(i.Authorization=`Bearer ${s.jwt}`);const o=(r=s==null?void 0:s.query)!==null&&r!==void 0?r:{};s!=null&&s.redirectTo&&(o.redirect_to=s.redirectTo);const a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",l=yield Ar(n,e,t+a,{headers:i,noResolveJson:s==null?void 0:s.noResolveJson},{},s==null?void 0:s.body);return s!=null&&s.xform?s==null?void 0:s.xform(l):{data:Object.assign({},l),error:null}})}function Ar(n,e,t,s,r,i){return h(this,null,function*(){const o=Or(e,s,r,i);let a;try{a=yield n(t,Object.assign({},o))}catch(l){throw new Xe(Q(l),0)}if(a.ok||(yield Ut(a)),s!=null&&s.noResolveJson)return a;try{return yield a.json()}catch(l){yield Ut(l)}})}function B(n){var e;let t=null;xr(n)&&(t=Object.assign({},n),n.expires_at||(t.expires_at=dr(n.expires_in)));const s=(e=n.user)!==null&&e!==void 0?e:n;return{data:{session:t,user:s},error:null}}function Lt(n){const e=B(n);return!e.error&&n.weak_password&&typeof n.weak_password=="object"&&Array.isArray(n.weak_password.reasons)&&n.weak_password.reasons.length&&n.weak_password.message&&typeof n.weak_password.message=="string"&&n.weak_password.reasons.reduce((t,s)=>t&&typeof s=="string",!0)&&(e.data.weak_password=n.weak_password),e}function K(n){var e;return{data:{user:(e=n.user)!==null&&e!==void 0?e:n},error:null}}function Rr(n){return{data:n,error:null}}function Ir(n){const{action_link:e,email_otp:t,hashed_token:s,redirect_to:r,verification_type:i}=n,o=Tr(n,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),a={action_link:e,email_otp:t,hashed_token:s,redirect_to:r,verification_type:i},l=Object.assign({},o);return{data:{properties:a,user:l},error:null}}function Cr(n){return n}function xr(n){return n.access_token&&n.refresh_token&&n.expires_in}const We=["global","local","others"];var Ur=function(n,e){var t={};for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&e.indexOf(s)<0&&(t[s]=n[s]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,s=Object.getOwnPropertySymbols(n);r<s.length;r++)e.indexOf(s[r])<0&&Object.prototype.propertyIsEnumerable.call(n,s[r])&&(t[s[r]]=n[s[r]]);return t};class Lr{constructor({url:e="",headers:t={},fetch:s}){this.url=e,this.headers=t,this.fetch=es(s),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}signOut(s){return h(this,arguments,function*(e,t=We[0]){if(We.indexOf(t)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${We.join(", ")}`);try{return yield S(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(r){if(m(r))return{data:null,error:r};throw r}})}inviteUserByEmail(s){return h(this,arguments,function*(e,t={}){try{return yield S(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:K})}catch(r){if(m(r))return{data:{user:null},error:r};throw r}})}generateLink(e){return h(this,null,function*(){try{const{options:t}=e,s=Ur(e,["options"]),r=Object.assign(Object.assign({},s),t);return"newEmail"in s&&(r.new_email=s==null?void 0:s.newEmail,delete r.newEmail),yield S(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:r,headers:this.headers,xform:Ir,redirectTo:t==null?void 0:t.redirectTo})}catch(t){if(m(t))return{data:{properties:null,user:null},error:t};throw t}})}createUser(e){return h(this,null,function*(){try{return yield S(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:K})}catch(t){if(m(t))return{data:{user:null},error:t};throw t}})}listUsers(e){return h(this,null,function*(){var t,s,r,i,o,a,l;try{const c={nextPage:null,lastPage:0,total:0},u=yield S(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(s=(t=e==null?void 0:e.page)===null||t===void 0?void 0:t.toString())!==null&&s!==void 0?s:"",per_page:(i=(r=e==null?void 0:e.perPage)===null||r===void 0?void 0:r.toString())!==null&&i!==void 0?i:""},xform:Cr});if(u.error)throw u.error;const d=yield u.json(),f=(o=u.headers.get("x-total-count"))!==null&&o!==void 0?o:0,g=(l=(a=u.headers.get("link"))===null||a===void 0?void 0:a.split(","))!==null&&l!==void 0?l:[];return g.length>0&&(g.forEach(p=>{const w=parseInt(p.split(";")[0].split("=")[1].substring(0,1)),_=JSON.parse(p.split(";")[1].split("=")[1]);c[`${_}Page`]=w}),c.total=parseInt(f)),{data:Object.assign(Object.assign({},d),c),error:null}}catch(c){if(m(c))return{data:{users:[]},error:c};throw c}})}getUserById(e){return h(this,null,function*(){oe(e);try{return yield S(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:K})}catch(t){if(m(t))return{data:{user:null},error:t};throw t}})}updateUserById(e,t){return h(this,null,function*(){oe(e);try{return yield S(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:K})}catch(s){if(m(s))return{data:{user:null},error:s};throw s}})}deleteUser(e,t=!1){return h(this,null,function*(){oe(e);try{return yield S(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:K})}catch(s){if(m(s))return{data:{user:null},error:s};throw s}})}_listFactors(e){return h(this,null,function*(){oe(e.userId);try{const{data:t,error:s}=yield S(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:r=>({data:{factors:r},error:null})});return{data:t,error:s}}catch(t){if(m(t))return{data:null,error:t};throw t}})}_deleteFactor(e){return h(this,null,function*(){oe(e.userId),oe(e.id);try{return{data:yield S(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(t){if(m(t))return{data:null,error:t};throw t}})}}function Dt(n={}){return{getItem:e=>n[e]||null,setItem:(e,t)=>{n[e]=t},removeItem:e=>{delete n[e]}}}function Dr(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(n){typeof self!="undefined"&&(self.globalThis=self)}}const ae={debug:!!(globalThis&&Zt()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class ts extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class qr extends ts{}function Br(n,e,t){return h(this,null,function*(){ae.debug;const s=new globalThis.AbortController;return e>0&&setTimeout(()=>{s.abort(),ae.debug},e),yield Promise.resolve().then(()=>globalThis.navigator.locks.request(n,e===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:s.signal},r=>h(null,null,function*(){if(r){ae.debug;try{return yield t()}finally{ae.debug}}else{if(e===0)throw ae.debug,new qr(`Acquiring an exclusive Navigator LockManager lock "${n}" immediately failed`);if(ae.debug)try{const i=yield globalThis.navigator.locks.query()}catch(i){}return yield t()}})))})}Dr();const Nr={url:Qs,storageKey:Ys,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:Xs,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};function qt(n,e,t){return h(this,null,function*(){return yield t()})}const le={};class we{constructor(e){var t,s;this.userStorage=null,this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=we.nextInstanceID,we.nextInstanceID+=1,this.instanceID>0&&D();const r=Object.assign(Object.assign({},Nr),e);if(this.logDebugMessages=!!r.debug,typeof r.debug=="function"&&(this.logger=r.debug),this.persistSession=r.persistSession,this.storageKey=r.storageKey,this.autoRefreshToken=r.autoRefreshToken,this.admin=new Lr({url:r.url,headers:r.headers,fetch:r.fetch}),this.url=r.url,this.headers=r.headers,this.fetch=es(r.fetch),this.lock=r.lock||qt,this.detectSessionInUrl=r.detectSessionInUrl,this.flowType=r.flowType,this.hasCustomAuthorizationHeader=r.hasCustomAuthorizationHeader,r.lock?this.lock=r.lock:D()&&(!((t=globalThis==null?void 0:globalThis.navigator)===null||t===void 0)&&t.locks)?this.lock=Br:this.lock=qt,this.jwks||(this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER),this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?(r.storage?this.storage=r.storage:Zt()?this.storage=globalThis.localStorage:(this.memoryStorage={},this.storage=Dt(this.memoryStorage)),r.userStorage&&(this.userStorage=r.userStorage)):(this.memoryStorage={},this.storage=Dt(this.memoryStorage)),D()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(i){}(s=this.broadcastChannel)===null||s===void 0||s.addEventListener("message",i=>h(this,null,function*(){this._debug("received broadcast notification from other tab or client",i),yield this._notifyAllSubscribers(i.data.event,i.data.session,!1)}))}this.initialize()}get jwks(){var e,t;return(t=(e=le[this.storageKey])===null||e===void 0?void 0:e.jwks)!==null&&t!==void 0?t:{keys:[]}}set jwks(e){le[this.storageKey]=Object.assign(Object.assign({},le[this.storageKey]),{jwks:e})}get jwks_cached_at(){var e,t;return(t=(e=le[this.storageKey])===null||e===void 0?void 0:e.cachedAt)!==null&&t!==void 0?t:Number.MIN_SAFE_INTEGER}set jwks_cached_at(e){le[this.storageKey]=Object.assign(Object.assign({},le[this.storageKey]),{cachedAt:e})}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${Vt}) ${new Date().toISOString()}`,...e),this}initialize(){return h(this,null,function*(){return this.initializePromise?yield this.initializePromise:(this.initializePromise=h(this,null,function*(){return yield this._acquireLock(-1,()=>h(this,null,function*(){return yield this._initialize()}))}),yield this.initializePromise)})}_initialize(){return h(this,null,function*(){var e;try{const t=gr(window.location.href);let s="none";if(this._isImplicitGrantCallback(t)?s="implicit":(yield this._isPKCECallback(t))&&(s="pkce"),D()&&this.detectSessionInUrl&&s!=="none"){const{data:r,error:i}=yield this._getSessionFromURL(t,s);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),ir(i)){const l=(e=i.details)===null||e===void 0?void 0:e.code;if(l==="identity_already_exists"||l==="identity_not_found"||l==="single_identity_not_deletable")return{error:i}}return yield this._removeSession(),{error:i}}const{session:o,redirectType:a}=r;return this._debug("#_initialize()","detected session in URL",o,"redirect type",a),yield this._saveSession(o),setTimeout(()=>h(this,null,function*(){a==="recovery"?yield this._notifyAllSubscribers("PASSWORD_RECOVERY",o):yield this._notifyAllSubscribers("SIGNED_IN",o)}),0),{error:null}}return yield this._recoverAndRefresh(),{error:null}}catch(t){return m(t)?{error:t}:{error:new Yt("Unexpected error during initialization",t)}}finally{yield this._handleVisibilityChange(),this._debug("#_initialize()","end")}})}signInAnonymously(e){return h(this,null,function*(){var t,s,r;try{const i=yield S(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(s=(t=e==null?void 0:e.options)===null||t===void 0?void 0:t.data)!==null&&s!==void 0?s:{},gotrue_meta_security:{captcha_token:(r=e==null?void 0:e.options)===null||r===void 0?void 0:r.captchaToken}},xform:B}),{data:o,error:a}=i;if(a||!o)return{data:{user:null,session:null},error:a};const l=o.session,c=o.user;return o.session&&(yield this._saveSession(o.session),yield this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:c,session:l},error:null}}catch(i){if(m(i))return{data:{user:null,session:null},error:i};throw i}})}signUp(e){return h(this,null,function*(){var t,s,r;try{let i;if("email"in e){const{email:u,password:d,options:f}=e;let g=null,p=null;this.flowType==="pkce"&&([g,p]=yield ne(this.storage,this.storageKey)),i=yield S(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:f==null?void 0:f.emailRedirectTo,body:{email:u,password:d,data:(t=f==null?void 0:f.data)!==null&&t!==void 0?t:{},gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken},code_challenge:g,code_challenge_method:p},xform:B})}else if("phone"in e){const{phone:u,password:d,options:f}=e;i=yield S(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:u,password:d,data:(s=f==null?void 0:f.data)!==null&&s!==void 0?s:{},channel:(r=f==null?void 0:f.channel)!==null&&r!==void 0?r:"sms",gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken}},xform:B})}else throw new je("You must provide either an email or phone number and a password");const{data:o,error:a}=i;if(a||!o)return{data:{user:null,session:null},error:a};const l=o.session,c=o.user;return o.session&&(yield this._saveSession(o.session),yield this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:c,session:l},error:null}}catch(i){if(m(i))return{data:{user:null,session:null},error:i};throw i}})}signInWithPassword(e){return h(this,null,function*(){try{let t;if("email"in e){const{email:i,password:o,options:a}=e;t=yield S(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:i,password:o,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:Lt})}else if("phone"in e){const{phone:i,password:o,options:a}=e;t=yield S(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:i,password:o,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:Lt})}else throw new je("You must provide either an email or phone number and a password");const{data:s,error:r}=t;return r?{data:{user:null,session:null},error:r}:!s||!s.session||!s.user?{data:{user:null,session:null},error:new Pe}:(s.session&&(yield this._saveSession(s.session),yield this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:Object.assign({user:s.user,session:s.session},s.weak_password?{weakPassword:s.weak_password}:null),error:r})}catch(t){if(m(t))return{data:{user:null,session:null},error:t};throw t}})}signInWithOAuth(e){return h(this,null,function*(){var t,s,r,i;return yield this._handleProviderSignIn(e.provider,{redirectTo:(t=e.options)===null||t===void 0?void 0:t.redirectTo,scopes:(s=e.options)===null||s===void 0?void 0:s.scopes,queryParams:(r=e.options)===null||r===void 0?void 0:r.queryParams,skipBrowserRedirect:(i=e.options)===null||i===void 0?void 0:i.skipBrowserRedirect})})}exchangeCodeForSession(e){return h(this,null,function*(){return yield this.initializePromise,this._acquireLock(-1,()=>h(this,null,function*(){return this._exchangeCodeForSession(e)}))})}signInWithWeb3(e){return h(this,null,function*(){const{chain:t}=e;if(t==="solana")return yield this.signInWithSolana(e);throw new Error(`@supabase/auth-js: Unsupported chain "${t}"`)})}signInWithSolana(e){return h(this,null,function*(){var t,s,r,i,o,a,l,c,u,d,f,g;let p,w;if("message"in e)p=e.message,w=e.signature;else{const{chain:_,wallet:j,statement:A,options:y}=e;let E;if(D())if(typeof j=="object")E=j;else{const P=window;if("solana"in P&&typeof P.solana=="object"&&("signIn"in P.solana&&typeof P.solana.signIn=="function"||"signMessage"in P.solana&&typeof P.solana.signMessage=="function"))E=P.solana;else throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if(typeof j!="object"||!(y!=null&&y.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");E=j}const O=new URL((t=y==null?void 0:y.url)!==null&&t!==void 0?t:window.location.href);if("signIn"in E&&E.signIn){const P=yield E.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},y==null?void 0:y.signInWithSolana),{version:"1",domain:O.host,uri:O.href}),A?{statement:A}:null));let R;if(Array.isArray(P)&&P[0]&&typeof P[0]=="object")R=P[0];else if(P&&typeof P=="object"&&"signedMessage"in P&&"signature"in P)R=P;else throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in R&&"signature"in R&&(typeof R.signedMessage=="string"||R.signedMessage instanceof Uint8Array)&&R.signature instanceof Uint8Array)p=typeof R.signedMessage=="string"?R.signedMessage:new TextDecoder().decode(R.signedMessage),w=R.signature;else throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in E)||typeof E.signMessage!="function"||!("publicKey"in E)||typeof E!="object"||!E.publicKey||!("toBase58"in E.publicKey)||typeof E.publicKey.toBase58!="function")throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");p=[`${O.host} wants you to sign in with your Solana account:`,E.publicKey.toBase58(),...A?["",A,""]:[""],"Version: 1",`URI: ${O.href}`,`Issued At: ${(r=(s=y==null?void 0:y.signInWithSolana)===null||s===void 0?void 0:s.issuedAt)!==null&&r!==void 0?r:new Date().toISOString()}`,...!((i=y==null?void 0:y.signInWithSolana)===null||i===void 0)&&i.notBefore?[`Not Before: ${y.signInWithSolana.notBefore}`]:[],...!((o=y==null?void 0:y.signInWithSolana)===null||o===void 0)&&o.expirationTime?[`Expiration Time: ${y.signInWithSolana.expirationTime}`]:[],...!((a=y==null?void 0:y.signInWithSolana)===null||a===void 0)&&a.chainId?[`Chain ID: ${y.signInWithSolana.chainId}`]:[],...!((l=y==null?void 0:y.signInWithSolana)===null||l===void 0)&&l.nonce?[`Nonce: ${y.signInWithSolana.nonce}`]:[],...!((c=y==null?void 0:y.signInWithSolana)===null||c===void 0)&&c.requestId?[`Request ID: ${y.signInWithSolana.requestId}`]:[],...!((d=(u=y==null?void 0:y.signInWithSolana)===null||u===void 0?void 0:u.resources)===null||d===void 0)&&d.length?["Resources",...y.signInWithSolana.resources.map(R=>`- ${R}`)]:[]].join(`
`);const P=yield E.signMessage(new TextEncoder().encode(p),"utf8");if(!P||!(P instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");w=P}}try{const{data:_,error:j}=yield S(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:p,signature:hr(w)},!((f=e.options)===null||f===void 0)&&f.captchaToken?{gotrue_meta_security:{captcha_token:(g=e.options)===null||g===void 0?void 0:g.captchaToken}}:null),xform:B});if(j)throw j;return!_||!_.session||!_.user?{data:{user:null,session:null},error:new Pe}:(_.session&&(yield this._saveSession(_.session),yield this._notifyAllSubscribers("SIGNED_IN",_.session)),{data:Object.assign({},_),error:j})}catch(_){if(m(_))return{data:{user:null,session:null},error:_};throw _}})}_exchangeCodeForSession(e){return h(this,null,function*(){const t=yield V(this.storage,`${this.storageKey}-code-verifier`),[s,r]=(t!=null?t:"").split("/");try{const{data:i,error:o}=yield S(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:s},xform:B});if(yield F(this.storage,`${this.storageKey}-code-verifier`),o)throw o;return!i||!i.session||!i.user?{data:{user:null,session:null,redirectType:null},error:new Pe}:(i.session&&(yield this._saveSession(i.session),yield this._notifyAllSubscribers("SIGNED_IN",i.session)),{data:Object.assign(Object.assign({},i),{redirectType:r!=null?r:null}),error:o})}catch(i){if(m(i))return{data:{user:null,session:null,redirectType:null},error:i};throw i}})}signInWithIdToken(e){return h(this,null,function*(){try{const{options:t,provider:s,token:r,access_token:i,nonce:o}=e,a=yield S(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:s,id_token:r,access_token:i,nonce:o,gotrue_meta_security:{captcha_token:t==null?void 0:t.captchaToken}},xform:B}),{data:l,error:c}=a;return c?{data:{user:null,session:null},error:c}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new Pe}:(l.session&&(yield this._saveSession(l.session),yield this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:l,error:c})}catch(t){if(m(t))return{data:{user:null,session:null},error:t};throw t}})}signInWithOtp(e){return h(this,null,function*(){var t,s,r,i,o;try{if("email"in e){const{email:a,options:l}=e;let c=null,u=null;this.flowType==="pkce"&&([c,u]=yield ne(this.storage,this.storageKey));const{error:d}=yield S(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:a,data:(t=l==null?void 0:l.data)!==null&&t!==void 0?t:{},create_user:(s=l==null?void 0:l.shouldCreateUser)!==null&&s!==void 0?s:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},code_challenge:c,code_challenge_method:u},redirectTo:l==null?void 0:l.emailRedirectTo});return{data:{user:null,session:null},error:d}}if("phone"in e){const{phone:a,options:l}=e,{data:c,error:u}=yield S(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:a,data:(r=l==null?void 0:l.data)!==null&&r!==void 0?r:{},create_user:(i=l==null?void 0:l.shouldCreateUser)!==null&&i!==void 0?i:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},channel:(o=l==null?void 0:l.channel)!==null&&o!==void 0?o:"sms"}});return{data:{user:null,session:null,messageId:c==null?void 0:c.message_id},error:u}}throw new je("You must provide either an email or phone number.")}catch(a){if(m(a))return{data:{user:null,session:null},error:a};throw a}})}verifyOtp(e){return h(this,null,function*(){var t,s;try{let r,i;"options"in e&&(r=(t=e.options)===null||t===void 0?void 0:t.redirectTo,i=(s=e.options)===null||s===void 0?void 0:s.captchaToken);const{data:o,error:a}=yield S(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:i}}),redirectTo:r,xform:B});if(a)throw a;if(!o)throw new Error("An error occurred on token verification.");const l=o.session,c=o.user;return l!=null&&l.access_token&&(yield this._saveSession(l),yield this._notifyAllSubscribers(e.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",l)),{data:{user:c,session:l},error:null}}catch(r){if(m(r))return{data:{user:null,session:null},error:r};throw r}})}signInWithSSO(e){return h(this,null,function*(){var t,s,r;try{let i=null,o=null;return this.flowType==="pkce"&&([i,o]=yield ne(this.storage,this.storageKey)),yield S(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:(s=(t=e.options)===null||t===void 0?void 0:t.redirectTo)!==null&&s!==void 0?s:void 0}),!((r=e==null?void 0:e.options)===null||r===void 0)&&r.captchaToken?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:o}),headers:this.headers,xform:Rr})}catch(i){if(m(i))return{data:null,error:i};throw i}})}reauthenticate(){return h(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>h(this,null,function*(){return yield this._reauthenticate()}))})}_reauthenticate(){return h(this,null,function*(){try{return yield this._useSession(e=>h(this,null,function*(){const{data:{session:t},error:s}=e;if(s)throw s;if(!t)throw new W;const{error:r}=yield S(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:r}}))}catch(e){if(m(e))return{data:{user:null,session:null},error:e};throw e}})}resend(e){return h(this,null,function*(){try{const t=`${this.url}/resend`;if("email"in e){const{email:s,type:r,options:i}=e,{error:o}=yield S(this.fetch,"POST",t,{headers:this.headers,body:{email:s,type:r,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}},redirectTo:i==null?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:o}}else if("phone"in e){const{phone:s,type:r,options:i}=e,{data:o,error:a}=yield S(this.fetch,"POST",t,{headers:this.headers,body:{phone:s,type:r,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:o==null?void 0:o.message_id},error:a}}throw new je("You must provide either an email or phone number and a type")}catch(t){if(m(t))return{data:{user:null,session:null},error:t};throw t}})}getSession(){return h(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>h(this,null,function*(){return this._useSession(t=>h(this,null,function*(){return t}))}))})}_acquireLock(e,t){return h(this,null,function*(){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){const s=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=h(this,null,function*(){return yield s,yield t()});return this.pendingInLock.push(h(this,null,function*(){try{yield r}catch(i){}})),r}return yield this.lock(`lock:${this.storageKey}`,e,()=>h(this,null,function*(){this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const s=t();for(this.pendingInLock.push(h(this,null,function*(){try{yield s}catch(r){}})),yield s;this.pendingInLock.length;){const r=[...this.pendingInLock];yield Promise.all(r),this.pendingInLock.splice(0,r.length)}return yield s}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}}))}finally{this._debug("#_acquireLock","end")}})}_useSession(e){return h(this,null,function*(){this._debug("#_useSession","begin");try{const t=yield this.__loadSession();return yield e(t)}finally{this._debug("#_useSession","end")}})}__loadSession(){return h(this,null,function*(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let e=null;const t=yield V(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),t!==null&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),yield this._removeSession())),!e)return{data:{session:null},error:null};const s=e.expires_at?e.expires_at*1e3-Date.now()<Be:!1;if(this._debug("#__loadSession()",`session has${s?"":" not"} expired`,"expires_at",e.expires_at),!s){if(this.userStorage){const o=yield V(this.userStorage,this.storageKey+"-user");o!=null&&o.user?e.user=o.user:e.user=Fe()}if(this.storage.isServer&&e.user){let o=this.suppressGetSessionWarning;e=new Proxy(e,{get:(l,c,u)=>(!o&&c==="user"&&(o=!0,this.suppressGetSessionWarning=!0),Reflect.get(l,c,u))})}return{data:{session:e},error:null}}const{session:r,error:i}=yield this._callRefreshToken(e.refresh_token);return i?{data:{session:null},error:i}:{data:{session:r},error:null}}finally{this._debug("#__loadSession()","end")}})}getUser(e){return h(this,null,function*(){return e?yield this._getUser(e):(yield this.initializePromise,yield this._acquireLock(-1,()=>h(this,null,function*(){return yield this._getUser()})))})}_getUser(e){return h(this,null,function*(){try{return e?yield S(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:K}):yield this._useSession(t=>h(this,null,function*(){var s,r,i;const{data:o,error:a}=t;if(a)throw a;return!(!((s=o.session)===null||s===void 0)&&s.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new W}:yield S(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(i=(r=o.session)===null||r===void 0?void 0:r.access_token)!==null&&i!==void 0?i:void 0,xform:K})}))}catch(t){if(m(t))return rr(t)&&(yield this._removeSession(),yield F(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:t};throw t}})}updateUser(s){return h(this,arguments,function*(e,t={}){return yield this.initializePromise,yield this._acquireLock(-1,()=>h(this,null,function*(){return yield this._updateUser(e,t)}))})}_updateUser(s){return h(this,arguments,function*(e,t={}){try{return yield this._useSession(r=>h(this,null,function*(){const{data:i,error:o}=r;if(o)throw o;if(!i.session)throw new W;const a=i.session;let l=null,c=null;this.flowType==="pkce"&&e.email!=null&&([l,c]=yield ne(this.storage,this.storageKey));const{data:u,error:d}=yield S(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:t==null?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:l,code_challenge_method:c}),jwt:a.access_token,xform:K});if(d)throw d;return a.user=u.user,yield this._saveSession(a),yield this._notifyAllSubscribers("USER_UPDATED",a),{data:{user:a.user},error:null}}))}catch(r){if(m(r))return{data:{user:null},error:r};throw r}})}setSession(e){return h(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>h(this,null,function*(){return yield this._setSession(e)}))})}_setSession(e){return h(this,null,function*(){try{if(!e.access_token||!e.refresh_token)throw new W;const t=Date.now()/1e3;let s=t,r=!0,i=null;const{payload:o}=Me(e.access_token);if(o.exp&&(s=o.exp,r=s<=t),r){const{session:a,error:l}=yield this._callRefreshToken(e.refresh_token);if(l)return{data:{user:null,session:null},error:l};if(!a)return{data:{user:null,session:null},error:null};i=a}else{const{data:a,error:l}=yield this._getUser(e.access_token);if(l)throw l;i={access_token:e.access_token,refresh_token:e.refresh_token,user:a.user,token_type:"bearer",expires_in:s-t,expires_at:s},yield this._saveSession(i),yield this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(t){if(m(t))return{data:{session:null,user:null},error:t};throw t}})}refreshSession(e){return h(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>h(this,null,function*(){return yield this._refreshSession(e)}))})}_refreshSession(e){return h(this,null,function*(){try{return yield this._useSession(t=>h(this,null,function*(){var s;if(!e){const{data:o,error:a}=t;if(a)throw a;e=(s=o.session)!==null&&s!==void 0?s:void 0}if(!(e!=null&&e.refresh_token))throw new W;const{session:r,error:i}=yield this._callRefreshToken(e.refresh_token);return i?{data:{user:null,session:null},error:i}:r?{data:{user:r.user,session:r},error:null}:{data:{user:null,session:null},error:null}}))}catch(t){if(m(t))return{data:{user:null,session:null},error:t};throw t}})}_getSessionFromURL(e,t){return h(this,null,function*(){try{if(!D())throw new Te("No browser detected.");if(e.error||e.error_description||e.error_code)throw new Te(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if(this.flowType==="pkce")throw new Ot("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new Te("Not a valid implicit grant flow url.");break;default:}if(t==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new Ot("No code detected.");const{data:A,error:y}=yield this._exchangeCodeForSession(e.code);if(y)throw y;const E=new URL(window.location.href);return E.searchParams.delete("code"),window.history.replaceState(window.history.state,"",E.toString()),{data:{session:A.session,redirectType:null},error:null}}const{provider_token:s,provider_refresh_token:r,access_token:i,refresh_token:o,expires_in:a,expires_at:l,token_type:c}=e;if(!i||!a||!o||!c)throw new Te("No session defined in URL");const u=Math.round(Date.now()/1e3),d=parseInt(a);let f=u+d;l&&(f=parseInt(l)),(f-u)*1e3<=ce;const p=f-d;u-p>=120||u-p<0;const{data:w,error:_}=yield this._getUser(i);if(_)throw _;const j={provider_token:s,provider_refresh_token:r,access_token:i,expires_in:d,expires_at:f,refresh_token:o,token_type:c,user:w.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:j,redirectType:e.type},error:null}}catch(s){if(m(s))return{data:{session:null,redirectType:null},error:s};throw s}})}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}_isPKCECallback(e){return h(this,null,function*(){const t=yield V(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)})}signOut(){return h(this,arguments,function*(e={scope:"global"}){return yield this.initializePromise,yield this._acquireLock(-1,()=>h(this,null,function*(){return yield this._signOut(e)}))})}_signOut(){return h(this,arguments,function*({scope:e}={scope:"global"}){return yield this._useSession(t=>h(this,null,function*(){var s;const{data:r,error:i}=t;if(i)return{error:i};const o=(s=r.session)===null||s===void 0?void 0:s.access_token;if(o){const{error:a}=yield this.admin.signOut(o,e);if(a&&!(sr(a)&&(a.status===404||a.status===401||a.status===403)))return{error:a}}return e!=="others"&&(yield this._removeSession(),yield F(this.storage,`${this.storageKey}-code-verifier`)),{error:null}}))})}onAuthStateChange(e){const t=fr(),s={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,s),h(this,null,function*(){yield this.initializePromise,yield this._acquireLock(-1,()=>h(this,null,function*(){this._emitInitialSession(t)}))}),{data:{subscription:s}}}_emitInitialSession(e){return h(this,null,function*(){return yield this._useSession(t=>h(this,null,function*(){var s,r;try{const{data:{session:i},error:o}=t;if(o)throw o;yield(s=this.stateChangeEmitters.get(e))===null||s===void 0?void 0:s.callback("INITIAL_SESSION",i),this._debug("INITIAL_SESSION","callback id",e,"session",i)}catch(i){yield(r=this.stateChangeEmitters.get(e))===null||r===void 0?void 0:r.callback("INITIAL_SESSION",null),this._debug("INITIAL_SESSION","callback id",e,"error",i)}}))})}resetPasswordForEmail(s){return h(this,arguments,function*(e,t={}){let r=null,i=null;this.flowType==="pkce"&&([r,i]=yield ne(this.storage,this.storageKey,!0));try{return yield S(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:i,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(o){if(m(o))return{data:null,error:o};throw o}})}getUserIdentities(){return h(this,null,function*(){var e;try{const{data:t,error:s}=yield this.getUser();if(s)throw s;return{data:{identities:(e=t.user.identities)!==null&&e!==void 0?e:[]},error:null}}catch(t){if(m(t))return{data:null,error:t};throw t}})}linkIdentity(e){return h(this,null,function*(){var t;try{const{data:s,error:r}=yield this._useSession(i=>h(this,null,function*(){var o,a,l,c,u;const{data:d,error:f}=i;if(f)throw f;const g=yield this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:(o=e.options)===null||o===void 0?void 0:o.redirectTo,scopes:(a=e.options)===null||a===void 0?void 0:a.scopes,queryParams:(l=e.options)===null||l===void 0?void 0:l.queryParams,skipBrowserRedirect:!0});return yield S(this.fetch,"GET",g,{headers:this.headers,jwt:(u=(c=d.session)===null||c===void 0?void 0:c.access_token)!==null&&u!==void 0?u:void 0})}));if(r)throw r;return D()&&!(!((t=e.options)===null||t===void 0)&&t.skipBrowserRedirect)&&window.location.assign(s==null?void 0:s.url),{data:{provider:e.provider,url:s==null?void 0:s.url},error:null}}catch(s){if(m(s))return{data:{provider:e.provider,url:null},error:s};throw s}})}unlinkIdentity(e){return h(this,null,function*(){try{return yield this._useSession(t=>h(this,null,function*(){var s,r;const{data:i,error:o}=t;if(o)throw o;return yield S(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:(r=(s=i.session)===null||s===void 0?void 0:s.access_token)!==null&&r!==void 0?r:void 0})}))}catch(t){if(m(t))return{data:null,error:t};throw t}})}_refreshAccessToken(e){return h(this,null,function*(){const t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{const s=Date.now();return yield _r(r=>h(this,null,function*(){return r>0&&(yield vr(200*Math.pow(2,r-1))),this._debug(t,"refreshing attempt",r),yield S(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:B})}),(r,i)=>{const o=200*Math.pow(2,r);return i&&Ne(i)&&Date.now()+o-s<ce})}catch(s){if(this._debug(t,"error",s),m(s))return{data:{session:null,user:null},error:s};throw s}finally{this._debug(t,"end")}})}_isValidSession(e){return typeof e=="object"&&e!==null&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}_handleProviderSignIn(e,t){return h(this,null,function*(){const s=yield this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",s),D()&&!t.skipBrowserRedirect&&window.location.assign(s),{data:{provider:e,url:s},error:null}})}_recoverAndRefresh(){return h(this,null,function*(){var e,t;const s="#_recoverAndRefresh()";this._debug(s,"begin");try{const r=yield V(this.storage,this.storageKey);if(r&&this.userStorage){let o=yield V(this.userStorage,this.storageKey+"-user");!this.storage.isServer&&Object.is(this.storage,this.userStorage)&&!o&&(o={user:r.user},yield ue(this.userStorage,this.storageKey+"-user",o)),r.user=(e=o==null?void 0:o.user)!==null&&e!==void 0?e:Fe()}else if(r&&!r.user&&!r.user){const o=yield V(this.storage,this.storageKey+"-user");o&&(o!=null&&o.user)?(r.user=o.user,yield F(this.storage,this.storageKey+"-user"),yield ue(this.storage,this.storageKey,r)):r.user=Fe()}if(this._debug(s,"session from storage",r),!this._isValidSession(r)){this._debug(s,"session is not valid"),r!==null&&(yield this._removeSession());return}const i=((t=r.expires_at)!==null&&t!==void 0?t:1/0)*1e3-Date.now()<Be;if(this._debug(s,`session has${i?"":" not"} expired with margin of ${Be}s`),i){if(this.autoRefreshToken&&r.refresh_token){const{error:o}=yield this._callRefreshToken(r.refresh_token);o&&(Ne(o)||(this._debug(s,"refresh failed with a non-retryable error, removing the session",o),yield this._removeSession()))}}else if(r.user&&r.user.__isUserNotAvailableProxy===!0)try{const{data:o,error:a}=yield this._getUser(r.access_token);!a&&(o!=null&&o.user)?(r.user=o.user,yield this._saveSession(r),yield this._notifyAllSubscribers("SIGNED_IN",r)):this._debug(s,"could not get user data, skipping SIGNED_IN notification")}catch(o){this._debug(s,"error getting user data, skipping SIGNED_IN notification",o)}else yield this._notifyAllSubscribers("SIGNED_IN",r)}catch(r){this._debug(s,"error",r);return}finally{this._debug(s,"end")}})}_callRefreshToken(e){return h(this,null,function*(){var t,s;if(!e)throw new W;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const r=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(r,"begin");try{this.refreshingDeferred=new Ae;const{data:i,error:o}=yield this._refreshAccessToken(e);if(o)throw o;if(!i.session)throw new W;yield this._saveSession(i.session),yield this._notifyAllSubscribers("TOKEN_REFRESHED",i.session);const a={session:i.session,error:null};return this.refreshingDeferred.resolve(a),a}catch(i){if(this._debug(r,"error",i),m(i)){const o={session:null,error:i};return Ne(i)||(yield this._removeSession()),(t=this.refreshingDeferred)===null||t===void 0||t.resolve(o),o}throw(s=this.refreshingDeferred)===null||s===void 0||s.reject(i),i}finally{this.refreshingDeferred=null,this._debug(r,"end")}})}_notifyAllSubscribers(e,t,s=!0){return h(this,null,function*(){const r=`#_notifyAllSubscribers(${e})`;this._debug(r,"begin",t,`broadcast = ${s}`);try{this.broadcastChannel&&s&&this.broadcastChannel.postMessage({event:e,session:t});const i=[],o=Array.from(this.stateChangeEmitters.values()).map(a=>h(this,null,function*(){try{yield a.callback(e,t)}catch(l){i.push(l)}}));if(yield Promise.all(o),i.length>0){for(let a=0;a<i.length;a+=1);throw i[0]}}finally{this._debug(r,"end")}})}_saveSession(e){return h(this,null,function*(){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0;const t=Object.assign({},e),s=t.user&&t.user.__isUserNotAvailableProxy===!0;if(this.userStorage){!s&&t.user&&(yield ue(this.userStorage,this.storageKey+"-user",{user:t.user}));const r=Object.assign({},t);delete r.user;const i=xt(r);yield ue(this.storage,this.storageKey,i)}else{const r=xt(t);yield ue(this.storage,this.storageKey,r)}})}_removeSession(){return h(this,null,function*(){this._debug("#_removeSession()"),yield F(this.storage,this.storageKey),yield F(this.storage,this.storageKey+"-code-verifier"),yield F(this.storage,this.storageKey+"-user"),this.userStorage&&(yield F(this.userStorage,this.storageKey+"-user")),yield this._notifyAllSubscribers("SIGNED_OUT",null)})}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&D()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){}}_startAutoRefresh(){return h(this,null,function*(){yield this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const e=setInterval(()=>this._autoRefreshTokenTick(),ce);this.autoRefreshTicker=e,e&&typeof e=="object"&&typeof e.unref=="function"?e.unref():typeof Deno!="undefined"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(e),setTimeout(()=>h(this,null,function*(){yield this.initializePromise,yield this._autoRefreshTokenTick()}),0)})}_stopAutoRefresh(){return h(this,null,function*(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)})}startAutoRefresh(){return h(this,null,function*(){this._removeVisibilityChangedCallback(),yield this._startAutoRefresh()})}stopAutoRefresh(){return h(this,null,function*(){this._removeVisibilityChangedCallback(),yield this._stopAutoRefresh()})}_autoRefreshTokenTick(){return h(this,null,function*(){this._debug("#_autoRefreshTokenTick()","begin");try{yield this._acquireLock(0,()=>h(this,null,function*(){try{const e=Date.now();try{return yield this._useSession(t=>h(this,null,function*(){const{data:{session:s}}=t;if(!s||!s.refresh_token||!s.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const r=Math.floor((s.expires_at*1e3-e)/ce);this._debug("#_autoRefreshTokenTick()",`access token expires in ${r} ticks, a tick lasts ${ce}ms, refresh threshold is ${Qe} ticks`),r<=Qe&&(yield this._callRefreshToken(s.refresh_token))}))}catch(t){}}finally{this._debug("#_autoRefreshTokenTick()","end")}}))}catch(e){if(e.isAcquireTimeout||e instanceof ts)this._debug("auto refresh token tick lock not available");else throw e}})}_handleVisibilityChange(){return h(this,null,function*(){if(this._debug("#_handleVisibilityChange()"),!D()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=()=>h(this,null,function*(){return yield this._onVisibilityChanged(!1)}),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),yield this._onVisibilityChanged(!0)}catch(e){}})}_onVisibilityChanged(e){return h(this,null,function*(){const t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),e||(yield this.initializePromise,yield this._acquireLock(-1,()=>h(this,null,function*(){if(document.visibilityState!=="visible"){this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}yield this._recoverAndRefresh()})))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()})}_getUrlForProvider(e,t,s){return h(this,null,function*(){const r=[`provider=${encodeURIComponent(t)}`];if(s!=null&&s.redirectTo&&r.push(`redirect_to=${encodeURIComponent(s.redirectTo)}`),s!=null&&s.scopes&&r.push(`scopes=${encodeURIComponent(s.scopes)}`),this.flowType==="pkce"){const[i,o]=yield ne(this.storage,this.storageKey),a=new URLSearchParams({code_challenge:`${encodeURIComponent(i)}`,code_challenge_method:`${encodeURIComponent(o)}`});r.push(a.toString())}if(s!=null&&s.queryParams){const i=new URLSearchParams(s.queryParams);r.push(i.toString())}return s!=null&&s.skipBrowserRedirect&&r.push(`skip_http_redirect=${s.skipBrowserRedirect}`),`${e}?${r.join("&")}`})}_unenroll(e){return h(this,null,function*(){try{return yield this._useSession(t=>h(this,null,function*(){var s;const{data:r,error:i}=t;return i?{data:null,error:i}:yield S(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:(s=r==null?void 0:r.session)===null||s===void 0?void 0:s.access_token})}))}catch(t){if(m(t))return{data:null,error:t};throw t}})}_enroll(e){return h(this,null,function*(){try{return yield this._useSession(t=>h(this,null,function*(){var s,r;const{data:i,error:o}=t;if(o)return{data:null,error:o};const a=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},e.factorType==="phone"?{phone:e.phone}:{issuer:e.issuer}),{data:l,error:c}=yield S(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:(s=i==null?void 0:i.session)===null||s===void 0?void 0:s.access_token});return c?{data:null,error:c}:(e.factorType==="totp"&&(!((r=l==null?void 0:l.totp)===null||r===void 0)&&r.qr_code)&&(l.totp.qr_code=`data:image/svg+xml;utf-8,${l.totp.qr_code}`),{data:l,error:null})}))}catch(t){if(m(t))return{data:null,error:t};throw t}})}_verify(e){return h(this,null,function*(){return this._acquireLock(-1,()=>h(this,null,function*(){try{return yield this._useSession(t=>h(this,null,function*(){var s;const{data:r,error:i}=t;if(i)return{data:null,error:i};const{data:o,error:a}=yield S(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:(s=r==null?void 0:r.session)===null||s===void 0?void 0:s.access_token});return a?{data:null,error:a}:(yield this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),yield this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:a})}))}catch(t){if(m(t))return{data:null,error:t};throw t}}))})}_challenge(e){return h(this,null,function*(){return this._acquireLock(-1,()=>h(this,null,function*(){try{return yield this._useSession(t=>h(this,null,function*(){var s;const{data:r,error:i}=t;return i?{data:null,error:i}:yield S(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:(s=r==null?void 0:r.session)===null||s===void 0?void 0:s.access_token})}))}catch(t){if(m(t))return{data:null,error:t};throw t}}))})}_challengeAndVerify(e){return h(this,null,function*(){const{data:t,error:s}=yield this._challenge({factorId:e.factorId});return s?{data:null,error:s}:yield this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})})}_listFactors(){return h(this,null,function*(){const{data:{user:e},error:t}=yield this.getUser();if(t)return{data:null,error:t};const s=(e==null?void 0:e.factors)||[],r=s.filter(o=>o.factor_type==="totp"&&o.status==="verified"),i=s.filter(o=>o.factor_type==="phone"&&o.status==="verified");return{data:{all:s,totp:r,phone:i},error:null}})}_getAuthenticatorAssuranceLevel(){return h(this,null,function*(){return this._acquireLock(-1,()=>h(this,null,function*(){return yield this._useSession(e=>h(this,null,function*(){var t,s;const{data:{session:r},error:i}=e;if(i)return{data:null,error:i};if(!r)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=Me(r.access_token);let a=null;o.aal&&(a=o.aal);let l=a;((s=(t=r.user.factors)===null||t===void 0?void 0:t.filter(d=>d.status==="verified"))!==null&&s!==void 0?s:[]).length>0&&(l="aal2");const u=o.amr||[];return{data:{currentLevel:a,nextLevel:l,currentAuthenticationMethods:u},error:null}}))}))})}fetchJwk(s){return h(this,arguments,function*(e,t={keys:[]}){let r=t.keys.find(l=>l.kid===e);if(r)return r;const i=Date.now();if(r=this.jwks.keys.find(l=>l.kid===e),r&&this.jwks_cached_at+er>i)return r;const{data:o,error:a}=yield S(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(a)throw a;return!o.keys||o.keys.length===0||(this.jwks=o,this.jwks_cached_at=i,r=o.keys.find(l=>l.kid===e),!r)?null:r})}getClaims(s){return h(this,arguments,function*(e,t={}){try{let r=e;if(!r){const{data:p,error:w}=yield this.getSession();if(w||!p.session)return{data:null,error:w};r=p.session.access_token}const{header:i,payload:o,signature:a,raw:{header:l,payload:c}}=Me(r);t!=null&&t.allowExpired||Er(o.exp);const u=!i.alg||i.alg.startsWith("HS")||!i.kid||!("crypto"in globalThis&&"subtle"in globalThis.crypto)?null:yield this.fetchJwk(i.kid,t!=null&&t.keys?{keys:t.keys}:t==null?void 0:t.jwks);if(!u){const{error:p}=yield this.getUser(r);if(p)throw p;return{data:{claims:o,header:i,signature:a},error:null}}const d=Pr(i.alg),f=yield crypto.subtle.importKey("jwk",u,d,!0,["verify"]);if(!(yield crypto.subtle.verify(d,f,a,ur(`${l}.${c}`))))throw new Ze("Invalid JWT signature");return{data:{claims:o,header:i,signature:a},error:null}}catch(r){if(m(r))return{data:null,error:r};throw r}})}}we.nextInstanceID=0;const Zr=we;export{Zr as A,Kr as F,gs as H,Jr as P,Yr as R,Xr as S,me as _,fs as n,Wr as r};
