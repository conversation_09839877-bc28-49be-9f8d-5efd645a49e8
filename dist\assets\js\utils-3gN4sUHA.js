var a=(r,e,t)=>new Promise((o,s)=>{var n=u=>{try{l(t.next(u))}catch(f){s(f)}},c=u=>{try{l(t.throw(u))}catch(f){s(f)}},l=u=>u.done?o(u.value):Promise.resolve(u.value).then(n,c);l((t=t.apply(r,e)).next())});import{c as v}from"./supabase-vendor-DDc5weSN.js";const M=(r,e=150)=>{if(!r)return"";const t=r.replace(/<[^>]*>/g,"").replace(/&nbsp;/g," ").replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/\s+/g," ").trim();if(t.length<=e)return t;const o=t.substring(0,e),s=o.lastIndexOf(" ");return s>e*.8?o.substring(0,s)+"...":o+"..."},F=(r,e=null)=>{if(e)return e;if(!r)return null;const t=r.match(/<img[^>]+src="([^"]*)"[^>]*>/i);return t?t[1]:null},B=r=>{if(!r)return"";const e=new Date(r),o=Math.abs(new Date-e),s=Math.ceil(o/(1e3*60*60*24));return s===1?"Yesterday":s<7?`${s} days ago`:s<30?`${Math.ceil(s/7)} weeks ago`:e.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})},O=(r,e)=>{let t;return function(...s){const n=()=>{clearTimeout(t),r(...s)};clearTimeout(t),t=setTimeout(n,e)}},b=!!(window.location.hostname==="localhost"||window.location.hostname==="[::1]"||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));function D(r){if("serviceWorker"in navigator){if(new URL("/",window.location.href).origin!==window.location.origin)return;window.addEventListener("load",()=>{const t="/sw.js";b?(y(t,r),navigator.serviceWorker.ready.then(()=>{})):p(t,r)})}}function p(r,e){navigator.serviceWorker.register(r).then(t=>{t.onupdatefound=()=>{const o=t.installing;o!=null&&(o.onstatechange=()=>{o.state==="installed"&&(navigator.serviceWorker.controller?e&&e.onUpdate&&e.onUpdate(t):e&&e.onSuccess&&e.onSuccess(t))})}}).catch(t=>{})}function y(r,e){fetch(r,{headers:{"Service-Worker":"script"}}).then(t=>{const o=t.headers.get("content-type");t.status===404||o!=null&&o.indexOf("javascript")===-1?navigator.serviceWorker.ready.then(s=>{s.unregister().then(()=>{window.location.reload()})}):p(r,e)}).catch(()=>{})}class T{constructor(){this.metrics={},this.observers=[],this.init()}init(){this.trackLCP(),this.trackFID(),this.trackCLS(),this.trackFCP(),this.trackTTFB(),this.trackNavigationTiming(),this.trackResourceTiming()}trackLCP(){if("PerformanceObserver"in window){const e=new PerformanceObserver(t=>{const o=t.getEntries(),s=o[o.length-1];this.metrics.lcp={value:s.startTime,element:s.element,timestamp:Date.now()},this.reportMetric("LCP",s.startTime)});e.observe({entryTypes:["largest-contentful-paint"]}),this.observers.push(e)}}trackFID(){if("PerformanceObserver"in window){const e=new PerformanceObserver(t=>{t.getEntries().forEach(s=>{this.metrics.fid={value:s.processingStart-s.startTime,timestamp:Date.now()},this.reportMetric("FID",s.processingStart-s.startTime)})});e.observe({entryTypes:["first-input"]}),this.observers.push(e)}}trackCLS(){if("PerformanceObserver"in window){let e=0;const t=new PerformanceObserver(o=>{o.getEntries().forEach(n=>{n.hadRecentInput||(e+=n.value)}),this.metrics.cls={value:e,timestamp:Date.now()},this.reportMetric("CLS",e)});t.observe({entryTypes:["layout-shift"]}),this.observers.push(t)}}trackFCP(){if("PerformanceObserver"in window){const e=new PerformanceObserver(t=>{t.getEntries().forEach(s=>{s.name==="first-contentful-paint"&&(this.metrics.fcp={value:s.startTime,timestamp:Date.now()},this.reportMetric("FCP",s.startTime))})});e.observe({entryTypes:["paint"]}),this.observers.push(e)}}trackTTFB(){if("performance"in window&&"getEntriesByType"in performance){const e=performance.getEntriesByType("navigation");if(e.length>0){const t=e[0],o=t.responseStart-t.requestStart;this.metrics.ttfb={value:o,timestamp:Date.now()},this.reportMetric("TTFB",o)}}}trackNavigationTiming(){if("performance"in window&&"getEntriesByType"in performance){const e=performance.getEntriesByType("navigation");if(e.length>0){const t=e[0];this.metrics.navigation={domContentLoaded:t.domContentLoadedEventEnd-t.domContentLoadedEventStart,loadComplete:t.loadEventEnd-t.loadEventStart,domInteractive:t.domInteractive-t.navigationStart,timestamp:Date.now()}}}}trackResourceTiming(){if("PerformanceObserver"in window){const e=new PerformanceObserver(t=>{t.getEntries().forEach(s=>{s.initiatorType==="img"&&this.trackImageLoad(s)})});e.observe({entryTypes:["resource"]}),this.observers.push(e)}}trackImageLoad(e){const t=e.responseEnd-e.startTime;this.metrics.images||(this.metrics.images=[]),this.metrics.images.push({url:e.name,loadTime:t,size:e.transferSize,timestamp:Date.now()}),t>1e3}reportMetric(e,t){window.gtag&&window.gtag("event","web_vitals",{event_category:"Performance",event_label:e,value:Math.round(t)})}getMetrics(){return this.metrics}getPerformanceScore(){const{lcp:e,fid:t,cls:o,fcp:s}=this.metrics;let n=100;return e&&(e.value>4e3?n-=30:e.value>2500&&(n-=15)),t&&(t.value>300?n-=25:t.value>100&&(n-=10)),o&&(o.value>.25?n-=25:o.value>.1&&(n-=10)),s&&(s.value>3e3?n-=20:s.value>1800&&(n-=10)),Math.max(0,n)}disconnect(){this.observers.forEach(e=>e.disconnect()),this.observers=[]}}const $=()=>{if(typeof window!="undefined"){const r=new T;return window.addEventListener("load",()=>{setTimeout(()=>{const e=r.getPerformanceScore()},5e3)}),r}return null},C="https://ckjpejxjpcfmlyopqabt.supabase.co",P="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNranBlanhqcGNmbWx5b3BxYWJ0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM3MTk5OTUsImV4cCI6MjA2OTI5NTk5NX0.sdinJPYznrITCIJBijRV2iwA0TSLLsTmLWtTYY37OLE",d=v(C,P,{auth:{persistSession:!1,autoRefreshToken:!1,detectSessionInUrl:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"sayari-blog@1.0.0"}}});let i={posts:null,authors:null,categories:null,tags:null};function m(r){return a(this,null,function*(){try{const e=yield fetch(`/data/static/${r}`);if(!e.ok)throw new Error(`Failed to load ${r}: ${e.status}`);return yield e.json()}catch(e){return null}})}function g(){return a(this,null,function*(){if(i.posts||(i.posts=yield m("posts.json")),i.posts)return i.posts;const{data:r,error:e}=yield d.from("posts").select(`
      id,
      title,
      slug,
      content,
      excerpt,
      author_id,
      published_at,
      status,
      featured_image_url,
      authors:author_id (
        id,
        username,
        display_name,
        bio
      )
    `).eq("status","published").order("published_at",{ascending:!1});if(e)throw new Error(`Failed to fetch posts: ${e.message}`);return r||[]})}function _(r){return a(this,null,function*(){return(yield g()).find(t=>t.slug===r)||null})}function j(r=0,e=10,t=""){return a(this,null,function*(){const o=yield g();let s=o;if(t&&t.trim()){const u=t.toLowerCase();s=o.filter(f=>{var h,w;return f.title.toLowerCase().includes(u)||((h=f.excerpt)==null?void 0:h.toLowerCase().includes(u))||((w=f.content)==null?void 0:w.toLowerCase().includes(u))})}const n=r*e,c=n+e;return{posts:s.slice(n,c),hasMore:c<s.length,total:s.length}})}function k(){return a(this,null,function*(){if(i.authors||(i.authors=yield m("authors.json")),i.authors)return i.authors;const{data:r,error:e}=yield d.from("users").select("id, user_login, display_name, user_registered").order("display_name");if(e)throw new Error(`Failed to fetch authors: ${e.message}`);return r||[]})}function x(r){return a(this,null,function*(){return(yield k()).find(t=>t.username===r)||null})}function W(r,e=""){return a(this,null,function*(){let o=(yield g()).filter(s=>s.author_id===r);if(e&&e.trim()){const s=e.toLowerCase();o=o.filter(n=>{var c,l;return n.title.toLowerCase().includes(s)||((c=n.excerpt)==null?void 0:c.toLowerCase().includes(s))||((l=n.content)==null?void 0:l.toLowerCase().includes(s))})}return o})}function E(){return a(this,null,function*(){if(i.categories||(i.categories=yield m("categories.json")),i.categories)return i.categories;const{data:r,error:e}=yield d.from("categories").select("id, name, slug, description").order("name");if(e)throw new Error(`Failed to fetch categories: ${e.message}`);return r||[]})}function A(r){return a(this,null,function*(){return(yield E()).find(t=>t.slug===r)||null})}function J(r,e=""){return a(this,null,function*(){let o=(yield g()).filter(s=>s.categories&&s.categories.some(n=>n.id===r));if(e&&e.trim()){const s=e.toLowerCase();o=o.filter(n=>{var c,l;return n.title.toLowerCase().includes(s)||((c=n.excerpt)==null?void 0:c.toLowerCase().includes(s))||((l=n.content)==null?void 0:l.toLowerCase().includes(s))})}return o})}function L(){return a(this,null,function*(){if(i.tags||(i.tags=yield m("tags.json")),i.tags)return i.tags;const{data:r,error:e}=yield d.from("tags").select("id, name, slug, description").order("name");if(e)throw new Error(`Failed to fetch tags: ${e.message}`);return r||[]})}function N(r){return a(this,null,function*(){return(yield L()).find(t=>t.slug===r)||null})}function U(r,e=""){return a(this,null,function*(){let o=(yield g()).filter(s=>s.tags&&s.tags.some(n=>n.id===r));if(e&&e.trim()){const s=e.toLowerCase();o=o.filter(n=>{var c,l;return n.title.toLowerCase().includes(s)||((c=n.excerpt)==null?void 0:c.toLowerCase().includes(s))||((l=n.content)==null?void 0:l.toLowerCase().includes(s))})}return o})}export{M as a,x as b,W as c,O as d,k as e,B as f,F as g,A as h,J as i,j,_ as k,g as l,N as m,U as n,$ as o,D as r};
