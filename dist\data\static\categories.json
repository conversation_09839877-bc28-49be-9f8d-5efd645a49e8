[{"id": "d1dfce0b-57fe-465f-80d6-de4ef3d4ea26", "name": "English", "slug": "english", "description": "<p class=\"\" data-start=\"212\" data-end=\"363\">Zayotech पर पाएं English में motivational, love, friendship और life से जुड़े best quotes, status और captions — social media और daily inspiration के लिए।</p>", "post_count": 17}, {"id": "e5d87820-f047-408f-84f5-98f695b01131", "name": "Hindi", "slug": "hindi", "description": "<p class=\"\" data-start=\"532\" data-end=\"653\">यहां आपको मिलेगा हिंदी में लिखी बेहतरीन शायरी, कोट्स, स्टेटस और शुभकामनाएं — हर emotion और खास मौके के लिए कुछ न कुछ खास।</p>", "post_count": 80}, {"id": "2b859be4-5b3f-41c6-b1a2-be8f0ac9ee23", "name": "Hindi Grammar", "slug": "hindi-grammar", "description": "<p class=\"\" data-start=\"683\" data-end=\"826\">हिंदी व्याकरण के topics जैसे संज्ञा, सर्वनाम, वचन, मुहावरे और विलोम शब्द को आसान भाषा में समझिए — छात्रों और प्रतियोगी परीक्षाओं के लिए उपयोगी।</p>", "post_count": 4}, {"id": "c14cc44c-34e6-4f18-aba2-1ca6bfa6f822", "name": "Images", "slug": "images", "description": "HD Hindi status images का ऐसा collection जो emotions को words से नहीं, visuals से बयां करे — WhatsApp, Instagram और Facebook पर शेयर करने के लिए perfect।", "post_count": 9}, {"id": "6c0e1fff-2351-4217-88e0-4bb5e55a3ee7", "name": "Jokes", "slug": "jokes", "description": "हंसी चाहिए? यहां पाएं हिंदी में सबसे मजेदार jokes और chutkule — जो हर उम्र के लिए हैं, WhatsApp और दोस्तों के बीच viral होने लायक।", "post_count": 4}, {"id": "dcf384fa-0b9b-4142-ac41-33cbef9a5253", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON><PERSON><PERSON>-shabd", "description": "<p class=\"\" data-start=\"982\" data-end=\"1090\">सभी महत्वपूर्ण शब्दों के पर्यायवाची शब्दों की लिस्ट — vocabulary सुधारने और हिंदी भाषा समझने के लिए उपयुक्त।</p>", "post_count": 3}, {"id": "2fb1e176-4a94-451a-bf16-ee4bbcee84e4", "name": "Poetries", "slug": "poetries", "description": "प्रेम, प्रेरणा, अकेलापन और जीवन की गहराइयों से जुड़ी हिंदी poetries का सुंदर संग्रह — दिल को छू लेने वाली कविताएं जो एहसास जगा दें।", "post_count": 7}, {"id": "f6dee1f8-b513-4256-a26a-fb4df93d3f86", "name": "Quotes", "slug": "quotes", "description": "पाएं life, love, motivation, friendship और sadness से जुड़ी inspiring quotes का बेहतरीन collection — हिंदी और English दोनों में express करने के लिए।", "post_count": 22}, {"id": "65b6a58c-f26f-48f8-81eb-c2866b719850", "name": "<PERSON><PERSON>", "slug": "sangya", "description": "<PERSON><PERSON> (संज्ञा) हिंदी व्याकरण का एक मुख्य भाग है। यहां जानिए संज्ञा की परिभाषा, प्रकार और आसान उदाहरण — छात्रों और प्रतियोगी परीक्षाओं के लिए उपयोगी जानकारी।", "post_count": 1}, {"id": "aac0a59d-6d0d-4c5f-a4da-311ddae26cd1", "name": "<PERSON><PERSON>", "slug": "<PERSON>hay<PERSON>", "description": "पाएं मोहब्बत, दर्द, दोस्ती और अकेलेपन से जुड़ी दिल को छू जाने वाली हिंदी Shayari — हर एहसास को बयां करने वाली खास लाइनों का कलेक्शन।", "post_count": 39}, {"id": "2226b475-2f7a-4e86-a921-40ed3cc0d488", "name": "Status", "slug": "status", "description": "WhatsApp, Instagram और Facebook के लिए पाएं stylish, emotional और attitude भरे हिंदी status — हर mood और feeling के लिए कुछ खास लाइनों का collection।", "post_count": 46}, {"id": "5f2078be-0354-4c09-87b3-591598b26fbd", "name": "Wishes", "slug": "wishes", "description": "Zay<PERSON>ch पर पाएँ Birthday, Anniversary और Festival जैसे खास मौकों के लिए दिल से निकली शुभकामनाएँ और emotional wishes – सब कुछ हिंदी में।", "post_count": 14}]