/*
  # Security Headers
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()

  # Performance Headers - Default cache for HTML
  Cache-Control: public, max-age=3600, must-revalidate

  # Preload critical resources
  Link: </assets/js/index.js>; rel=preload; as=script
  Link: </assets/css/index.css>; rel=preload; as=style

  # DNS prefetch for external domains
  Link: <https://ckjpejxjpcfmlyopqabt.supabase.co>; rel=dns-prefetch

# Static JavaScript assets with long cache
/assets/*.js
  Content-Type: application/javascript
  Cache-Control: public, max-age=31536000, immutable

# Static CSS assets with long cache
/assets/*.css
  Content-Type: text/css
  Cache-Control: public, max-age=31536000, immutable

# All JavaScript files
/*.js
  Content-Type: application/javascript
  Cache-Control: public, max-age=31536000, immutable

# Images with optimized caching
/*.jpg
  Cache-Control: public, max-age=2592000
  Vary: Accept
/*.jpeg
  Cache-Control: public, max-age=2592000
  Vary: Accept
/*.png
  Cache-Control: public, max-age=2592000
  Vary: Accept
/*.webp
  Cache-Control: public, max-age=2592000
  Vary: Accept
/*.avif
  Cache-Control: public, max-age=2592000
  Vary: Accept
/*.svg
  Cache-Control: public, max-age=2592000
  Content-Type: image/svg+xml

# Fonts with long cache
/*.woff
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: font/woff
/*.woff2
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: font/woff2

# Service Worker - no cache
/sw.js
  Cache-Control: no-cache, no-store, must-revalidate
  Content-Type: application/javascript

# Manifest
/manifest.json
  Cache-Control: public, max-age=86400
  Content-Type: application/json
