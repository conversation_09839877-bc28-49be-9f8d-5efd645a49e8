var p=(i,r,e)=>new Promise((c,t)=>{var g=o=>{try{n(e.next(o))}catch(l){t(l)}},m=o=>{try{n(e.throw(o))}catch(l){t(l)}},n=o=>o.done?c(o.value):Promise.resolve(o.value).then(g,m);n((e=e.apply(i,r)).next())});import{a as h,r as d,j as s}from"./react-vendor-DNThP37t.js";import{m as u,n as j}from"./utils-3gN4sUHA.js";import{P as N}from"./components-D616EIDv.js";import"./vendor-BttnBCBn.js";import"./supabase-vendor-DDc5weSN.js";const E=({searchQuery:i})=>{const{slug:r}=h(),[e,c]=d.useState([]),[t,g]=d.useState(null),[m,n]=d.useState(!0),[o,l]=d.useState(null);d.useEffect(()=>{r&&x()},[r,i]);const x=()=>p(null,null,function*(){try{n(!0);const a=yield u(r);if(!a)throw new Error("Tag not found");g(a);const f=yield j(a.id,i);c(f)}catch(a){l("Failed to load tag posts")}finally{n(!1)}});return m?s.jsx("div",{className:"main-grid",children:s.jsx("div",{className:"loading",children:"Loading posts..."})}):o?s.jsx("div",{className:"main-grid",children:s.jsx("div",{className:"error",children:o})}):e.length===0?s.jsx("div",{className:"main-grid",children:s.jsx("div",{className:"loading",children:i?`No posts found with tag "${t==null?void 0:t.name}" for "${i}"`:`No posts found with tag "${t==null?void 0:t.name}"`})}):s.jsxs(s.Fragment,{children:[t&&s.jsxs("div",{style:{textAlign:"center",marginBottom:"30px",padding:"20px",background:"white",border:"1px solid #f0f0f0"},children:[s.jsxs("h1",{style:{fontSize:"28px",fontWeight:"bold",color:"#333",marginBottom:"10px"},children:["#",t.name]}),t.description&&s.jsx("p",{style:{color:"#666",fontSize:"16px"},children:t.description}),s.jsxs("p",{style:{color:"#999",fontSize:"14px",marginTop:"10px"},children:[e.length," ",e.length===1?"post":"posts"]})]}),s.jsx("div",{className:"main-grid",children:e.map(a=>s.jsx(N,{post:a},a.id))})]})};export{E as default};
