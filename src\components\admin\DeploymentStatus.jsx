import { useState, useEffect } from 'react'
import { getDeploymentStatus, getDeploymentConfig } from '../../lib/deploymentService'

const DeploymentStatus = ({ postId, className = '' }) => {
  const [deployments, setDeployments] = useState([])
  const [loading, setLoading] = useState(true)
  const [config, setConfig] = useState(null)

  useEffect(() => {
    loadDeploymentData()
  }, [postId])

  const loadDeploymentData = async () => {
    try {
      setLoading(true)
      const [deploymentHistory, deploymentConfig] = await Promise.all([
        getDeploymentStatus(postId),
        Promise.resolve(getDeploymentConfig())
      ])
      
      setDeployments(deploymentHistory)
      setConfig(deploymentConfig)
    } catch (error) {
      console.error('Failed to load deployment data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return '✅'
      case 'failed':
        return '❌'
      case 'pending':
        return '⏳'
      default:
        return '❓'
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'success':
        return '#10b981'
      case 'failed':
        return '#ef4444'
      case 'pending':
        return '#f59e0b'
      default:
        return '#6b7280'
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString()
  }

  if (loading) {
    return (
      <div className={`deployment-status ${className}`}>
        <div className="loading">Loading deployment status...</div>
      </div>
    )
  }

  return (
    <div className={`deployment-status ${className}`}>
      <div className="deployment-header">
        <h3>Deployment Status</h3>
        {config?.configured ? (
          <div className="platform-info">
            <span className="platform-badge">
              📡 {config.platform}
            </span>
          </div>
        ) : (
          <div className="not-configured">
            <span className="warning-badge">
              ⚠️ Not Configured
            </span>
          </div>
        )}
      </div>

      {!config?.configured && (
        <div className="config-warning">
          <p>Deployment is not configured. To enable automatic deployment when publishing posts, set up one of the following:</p>
          <ul>
            <li><strong>Cloudflare Pages:</strong> Set VITE_CLOUDFLARE_WEBHOOK_URL or API credentials</li>
            <li><strong>Vercel:</strong> Set VITE_VERCEL_WEBHOOK_URL or API credentials</li>
            <li><strong>Netlify:</strong> Set VITE_NETLIFY_WEBHOOK_URL or API credentials</li>
            <li><strong>GitHub Pages:</strong> Set VITE_GITHUB_TOKEN and repository details</li>
          </ul>
        </div>
      )}

      {deployments.length > 0 ? (
        <div className="deployment-history">
          <h4>Recent Deployments</h4>
          <div className="deployment-list">
            {deployments.map((deployment) => (
              <div key={deployment.id} className="deployment-item">
                <div className="deployment-info">
                  <span className="status-icon">
                    {getStatusIcon(deployment.status)}
                  </span>
                  <div className="deployment-details">
                    <div className="platform-status">
                      <strong>{deployment.platform}</strong>
                      <span 
                        className="status-text"
                        style={{ color: getStatusColor(deployment.status) }}
                      >
                        {deployment.status}
                      </span>
                    </div>
                    <div className="deployment-time">
                      {formatDate(deployment.created_at)}
                    </div>
                    {deployment.details && deployment.status === 'failed' && (
                      <div className="error-details">
                        {deployment.details.error}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="no-deployments">
          <p>No deployments yet. Publish this post to trigger the first deployment.</p>
        </div>
      )}

      <style jsx>{`
        .deployment-status {
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          padding: 1rem;
          margin-top: 1rem;
        }

        .deployment-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
        }

        .deployment-header h3 {
          margin: 0;
          font-size: 1.1rem;
          color: #1f2937;
        }

        .platform-badge {
          background: #10b981;
          color: white;
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          font-size: 0.875rem;
          font-weight: 500;
        }

        .warning-badge {
          background: #f59e0b;
          color: white;
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          font-size: 0.875rem;
          font-weight: 500;
        }

        .config-warning {
          background: #fef3c7;
          border: 1px solid #f59e0b;
          border-radius: 6px;
          padding: 1rem;
          margin-bottom: 1rem;
        }

        .config-warning p {
          margin: 0 0 0.5rem 0;
          color: #92400e;
          font-weight: 500;
        }

        .config-warning ul {
          margin: 0;
          padding-left: 1.5rem;
          color: #92400e;
        }

        .config-warning li {
          margin-bottom: 0.25rem;
        }

        .deployment-history h4 {
          margin: 0 0 0.75rem 0;
          font-size: 1rem;
          color: #374151;
        }

        .deployment-list {
          space-y: 0.5rem;
        }

        .deployment-item {
          background: white;
          border: 1px solid #e5e7eb;
          border-radius: 6px;
          padding: 0.75rem;
          margin-bottom: 0.5rem;
        }

        .deployment-info {
          display: flex;
          align-items: flex-start;
          gap: 0.75rem;
        }

        .status-icon {
          font-size: 1.25rem;
          line-height: 1;
        }

        .deployment-details {
          flex: 1;
        }

        .platform-status {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 0.25rem;
        }

        .platform-status strong {
          color: #1f2937;
          text-transform: capitalize;
        }

        .status-text {
          font-size: 0.875rem;
          font-weight: 500;
          text-transform: capitalize;
        }

        .deployment-time {
          font-size: 0.875rem;
          color: #6b7280;
        }

        .error-details {
          margin-top: 0.5rem;
          padding: 0.5rem;
          background: #fef2f2;
          border: 1px solid #fecaca;
          border-radius: 4px;
          font-size: 0.875rem;
          color: #dc2626;
        }

        .no-deployments {
          text-align: center;
          padding: 2rem;
          color: #6b7280;
        }

        .no-deployments p {
          margin: 0;
        }

        .loading {
          text-align: center;
          padding: 1rem;
          color: #6b7280;
        }

        @media (max-width: 768px) {
          .deployment-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
          }

          .platform-status {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.25rem;
          }
        }
      `}</style>
    </div>
  )
}

export default DeploymentStatus
